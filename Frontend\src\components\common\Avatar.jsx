import React, { useState } from 'react';
import '../../styles/Avatar.css';

const Avatar = ({ 
  src, 
  alt = 'User Avatar', 
  size = 'medium', 
  className = '',
  fallbackIcon = '👤'
}) => {
  const [imageError, setImageError] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  const sizeClasses = {
    small: 'avatar--small',
    medium: 'avatar--medium',
    large: 'avatar--large'
  };

  const handleImageError = () => {
    setImageError(true);
  };

  const handleImageLoad = () => {
    setImageLoaded(true);
  };

  const showFallback = !src || imageError || !imageLoaded;

  return (
    <div className={`avatar ${sizeClasses[size]} ${className}`}>
      {src && !imageError && (
        <img
          src={src}
          alt={alt}
          className="avatar__image"
          onError={handleImageError}
          onLoad={handleImageLoad}
          style={{ display: imageLoaded ? 'block' : 'none' }}
        />
      )}
      {showFallback && (
        <div className="avatar__fallback">
          <span>{fallbackIcon}</span>
        </div>
      )}
    </div>
  );
};

export default Avatar;
