import React, { useState } from 'react';
import { NavLink } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { CoolMode } from '../magicui/cool-mode';
import '../../styles/AdminSidebar.css';

const AdminSidebar = ({ collapsed, isOpen, isMobile, onClose }) => {
  const { user } = useSelector((state) => state.auth);
  const [imageError, setImageError] = useState(false);

  const menuItems = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      path: '/admin/dashboard',
      icon: '📊'
    },
    {
      id: 'users',
      label: 'Users',
      path: '/admin/users',
      icon: '👥'
    },
    {
      id: 'content',
      label: 'Content',
      path: '/admin/content',
      icon: '📄'
    },
    {
      id: 'orders',
      label: 'Orders',
      path: '/admin/orders',
      icon: '🛒'
    },
    {
      id: 'analytics',
      label: 'Analytics',
      path: '/admin/analytics',
      icon: '📈'
    },
    {
      id: 'cms',
      label: 'CMS',
      path: '/admin/cms',
      icon: '📝'
    },
    {
      id: 'settings',
      label: 'Settings',
      path: '/admin/settings',
      icon: '⚙️'
    }
  ];

  const handleLinkClick = () => {
    if (isMobile) {
      onClose();
    }
  };

  return (
    <aside className={`admin-sidebar ${collapsed ? 'admin-sidebar--collapsed' : ''} ${isOpen ? 'admin-sidebar--open' : ''}`}>
      {/* Logo Section */}
      <div className="admin-sidebar__header">
        <CoolMode
          options={{
            particle: "⚡",
            particleCount: 6,
            colors: ["#EE3425", "#FFD700", "#FF6B35", "#4ECDC4"],
            size: 14,
            speed: 50,
            life: 1000
          }}
        >
          <div className="admin-sidebar__logo">
            <div className="admin-sidebar__logo-icon">
              <span>XO</span>
            </div>
            {!collapsed && (
              <div className="admin-sidebar__logo-text">
                <h3>Admin Panel</h3>
              </div>
            )}
          </div>
        </CoolMode>
      </div>

      {/* User Info */}
      <div className="admin-sidebar__user">
        <div className="admin-sidebar__user-avatar">
          {!imageError && user?.avatar ? (
            <img
              src={user.avatar}
              alt={user?.name || 'Admin'}
              onError={() => setImageError(true)}
            />
          ) : (
            <div className="admin-sidebar__user-avatar-fallback">
              <span>👤</span>
            </div>
          )}
        </div>
        {!collapsed && (
          <div className="admin-sidebar__user-info">
            <h4>{user?.name || 'Admin User'}</h4>
            <p>{user?.email || '<EMAIL>'}</p>
          </div>
        )}
      </div>

      {/* Navigation Menu */}
      <nav className="admin-sidebar__nav">
        <ul className="admin-sidebar__menu">
          {menuItems.map((item) => (
            <li key={item.id} className="admin-sidebar__menu-item">
              <NavLink
                to={item.path}
                className={({ isActive }) =>
                  `admin-sidebar__menu-link ${isActive ? 'admin-sidebar__menu-link--active' : ''}`
                }
                onClick={handleLinkClick}
              >
                <span className="admin-sidebar__menu-icon">
                  {item.icon}
                </span>
                {!collapsed && (
                  <span className="admin-sidebar__menu-label">
                    {item.label}
                  </span>
                )}
              </NavLink>
            </li>
          ))}
        </ul>
      </nav>

      {/* Footer */}
      <div className="admin-sidebar__footer">
        {!collapsed && (
          <div className="admin-sidebar__footer-content">
            <p className="admin-sidebar__version">
              Version 1.0.0
            </p>
          </div>
        )}
      </div>
    </aside>
  );
};

export default AdminSidebar;
