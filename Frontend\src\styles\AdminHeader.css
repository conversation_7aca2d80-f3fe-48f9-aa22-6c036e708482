/* Admin Header Styles */
.admin-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: var(--white);
  border-bottom: 1px solid var(--light-gray);
  box-shadow: var(--box-shadow-light);
  position: sticky;
  top: 0;
  z-index: var(--z-index-header);
}

.admin-header__left {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.admin-header__toggle {
  background: none;
  border: none;
  padding: var(--spacing-sm);
  border-radius: var(--border-radius);
  cursor: pointer;
  color: var(--text-color);
  transition: background-color 0.3s ease;
}

.admin-header__toggle:hover {
  background-color: var(--bg-gray);
}

.admin-header__toggle-icon {
  font-size: var(--heading5);
  display: block;
}

.admin-header__title h1 {
  color: var(--text-color);
  font-size: var(--heading4);
  font-weight: 600;
  margin: 0;
}

.admin-header__right {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.admin-header__stats {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.admin-header__stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-sm);
  background-color: var(--bg-gray);
  border-radius: var(--border-radius);
  min-width: 60px;
}

.admin-header__stat-label {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
  margin-bottom: 2px;
}

.admin-header__stat-value {
  font-size: var(--basefont);
  font-weight: 600;
  color: var(--primary-color);
}

/* Notifications */
.admin-header__notifications {
  position: relative;
}

.admin-header__notifications-btn {
  background: none;
  border: none;
  padding: var(--spacing-sm);
  border-radius: 50%;
  cursor: pointer;
  position: relative;
  transition: background-color 0.3s ease;
}

.admin-header__notifications-btn:hover {
  background-color: var(--bg-gray);
}

.admin-header__notifications-icon {
  font-size: var(--heading5);
  color: var(--text-color);
}

.admin-header__notifications-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: var(--btn-color);
  color: var(--white);
  font-size: var(--extrasmallfont);
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
}

.admin-header__notifications-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 320px;
  background-color: var(--white);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow);
  z-index: var(--z-index-tooltip);
  margin-top: var(--spacing-sm);
}

.admin-header__notifications-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--light-gray);
}

.admin-header__notifications-header h3 {
  font-size: var(--heading6);
  font-weight: 600;
  margin: 0;
  color: var(--text-color);
}

.admin-header__notifications-count {
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

.admin-header__notifications-list {
  max-height: 300px;
  overflow-y: auto;
}

.admin-header__notification-item {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--light-gray);
  transition: background-color 0.3s ease;
}

.admin-header__notification-item:hover {
  background-color: var(--bg-gray);
}

.admin-header__notification-item--unread {
  background-color: var(--primary-light-color);
}

.admin-header__notification-content p {
  font-size: var(--smallfont);
  color: var(--text-color);
  margin: 0 0 4px 0;
}

.admin-header__notification-time {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

.admin-header__notifications-footer {
  padding: var(--spacing-md);
  text-align: center;
}

.admin-header__notifications-view-all {
  background: none;
  border: none;
  color: var(--primary-color);
  font-size: var(--smallfont);
  font-weight: 500;
  cursor: pointer;
  text-decoration: underline;
}

/* User Menu */
.admin-header__user {
  position: relative;
}

.admin-header__user-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  background: none;
  border: none;
  padding: var(--spacing-sm);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.admin-header__user-btn:hover {
  background-color: var(--bg-gray);
}

.admin-header__user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
  background-color: var(--bg-gray);
}

.admin-header__user-avatar-fallback {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary-light-color);
  color: var(--primary-color);
  font-size: 18px;
}

.admin-header__user-name {
  font-size: var(--basefont);
  font-weight: 500;
  color: var(--text-color);
}

.admin-header__user-arrow {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  transition: transform 0.3s ease;
}

.admin-header__user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 240px;
  background-color: var(--white);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow);
  z-index: var(--z-index-tooltip);
  margin-top: var(--spacing-sm);
}

.admin-header__user-info {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--light-gray);
}

.admin-header__user-email {
  font-size: var(--smallfont);
  color: var(--text-color);
  margin: 0 0 4px 0;
}

.admin-header__user-role {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
  margin: 0;
}

.admin-header__user-menu {
  padding: var(--spacing-sm) 0;
}

.admin-header__user-menu-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  background: none;
  border: none;
  text-align: left;
  font-size: var(--smallfont);
  color: var(--text-color);
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.admin-header__user-menu-item:hover {
  background-color: var(--bg-gray);
}

.admin-header__user-menu-item--danger {
  color: var(--btn-color);
}

.admin-header__user-menu-divider {
  border: none;
  border-top: 1px solid var(--light-gray);
  margin: var(--spacing-sm) 0;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .admin-header {
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .admin-header__title h1 {
    font-size: var(--heading5);
  }

  .admin-header__stats {
    display: none;
  }

  .admin-header__user-name {
    display: none;
  }

  .admin-header__notifications-dropdown,
  .admin-header__user-dropdown {
    width: 280px;
  }
}

@media (max-width: 480px) {
  .admin-header__right {
    gap: var(--spacing-sm);
  }
}
