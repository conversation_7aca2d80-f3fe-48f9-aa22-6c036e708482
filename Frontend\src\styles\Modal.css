/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-index-modal);
  padding: var(--spacing-lg);
}

.modal {
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow-dark);
  max-height: 90vh;
  overflow-y: auto;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Modal Sizes */
.modal--small {
  width: 100%;
  max-width: 400px;
}

.modal--medium {
  width: 100%;
  max-width: 600px;
}

.modal--large {
  width: 100%;
  max-width: 800px;
}

.modal--full {
  width: 95%;
  max-width: 1200px;
}

/* Modal Header */
.modal__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--light-gray);
}

.modal__title {
  color: var(--text-color);
  font-size: var(--heading4);
  font-weight: 600;
  margin: 0;
}

.modal__close-btn {
  background: none;
  border: none;
  font-size: var(--heading5);
  color: var(--dark-gray);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--border-radius);
  transition: all 0.3s ease;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal__close-btn:hover {
  background-color: var(--bg-gray);
  color: var(--text-color);
}

/* Modal Content */
.modal__content {
  padding: var(--spacing-lg);
}

/* Tablet Responsive */
@media (max-width: 1024px) {
  .modal--large,
  .modal--full {
    width: 95%;
    max-width: 900px;
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .modal-overlay {
    padding: var(--spacing-md);
    align-items: flex-start;
    padding-top: var(--spacing-xl);
  }

  .modal {
    max-height: 90vh;
    width: 100%;
    margin: 0;
  }

  .modal--small,
  .modal--medium,
  .modal--large,
  .modal--full {
    width: 100%;
    max-width: none;
  }

  .modal__header {
    padding: var(--spacing-md);
    position: sticky;
    top: 0;
    background-color: var(--white);
    border-bottom: 1px solid var(--light-gray);
    z-index: 1;
  }

  .modal__content {
    padding: var(--spacing-md);
    overflow-y: auto;
    max-height: calc(90vh - 80px);
  }

  .modal__title {
    font-size: var(--heading5);
  }
}

/* Small Mobile */
@media (max-width: 480px) {
  .modal-overlay {
    padding: var(--spacing-sm);
    padding-top: var(--spacing-lg);
  }

  .modal {
    max-height: 95vh;
  }

  .modal__header {
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .modal__content {
    padding: var(--spacing-sm) var(--spacing-md);
    max-height: calc(95vh - 60px);
  }

  .modal__title {
    font-size: var(--heading6);
  }

  .modal__close-btn {
    width: 28px;
    height: 28px;
    font-size: var(--basefont);
  }
}

/* Extra small screens - Full screen modal */
@media (max-width: 360px) {
  .modal-overlay {
    padding: 0;
  }

  .modal {
    width: 100vw;
    height: 100vh;
    max-height: 100vh;
    border-radius: 0;
    margin: 0;
  }

  .modal__content {
    max-height: calc(100vh - 60px);
  }
}
