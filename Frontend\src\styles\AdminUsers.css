/* Admin Users Styles */
.admin-users {
  padding: var(--spacing-lg);
}

.admin-users__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-lg);
}

.admin-users__title h1 {
  color: var(--text-color);
  font-size: var(--heading2);
  font-weight: 600;
  margin: 0 0 var(--spacing-sm) 0;
}

.admin-users__title p {
  color: var(--dark-gray);
  font-size: var(--basefont);
  margin: 0;
}

.admin-users__actions {
  display: flex;
  gap: var(--spacing-md);
}

/* Loading State */
.admin-users__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  text-align: center;
}

.admin-users__spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--light-gray);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-md);
}

/* Filters */
.admin-users__filters {
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--box-shadow-light);
}

.admin-users__filter-group {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
  flex-wrap: wrap;
}

.admin-users__search {
  flex: 1;
  min-width: 200px;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
}

.admin-users__select {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  background-color: var(--white);
}

/* Stats */
.admin-users__stats {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.admin-users__stat {
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  padding: var(--spacing-lg);
  box-shadow: var(--box-shadow-light);
  text-align: center;
  min-width: 120px;
}

.admin-users__stat-value {
  display: block;
  font-size: var(--heading3);
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: var(--spacing-xs);
}

.admin-users__stat-label {
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

/* Bulk Actions */
.admin-users__bulk-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--primary-light-color);
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-lg);
}

.admin-users__bulk-buttons {
  display: flex;
  gap: var(--spacing-sm);
}

/* Table */
.admin-users__table-container {
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow-light);
  overflow: hidden;
  margin-bottom: var(--spacing-lg);
}

.admin-users__table {
  width: 100%;
  border-collapse: collapse;
}

.admin-users__table th,
.admin-users__table td {
  padding: var(--spacing-md);
  text-align: left;
  border-bottom: 1px solid var(--light-gray);
}

.admin-users__table th {
  background-color: var(--bg-gray);
  font-weight: 600;
  color: var(--text-color);
  font-size: var(--smallfont);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-users__table-row:hover {
  background-color: var(--bg-gray);
}

.admin-users__user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.admin-users__avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.admin-users__avatar-fallback {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary-light-color);
  color: var(--primary-color);
  font-size: 18px;
  flex-shrink: 0;
}

.admin-users__name {
  font-weight: 600;
  color: var(--text-color);
  font-size: var(--basefont);
}

.admin-users__email {
  color: var(--dark-gray);
  font-size: var(--smallfont);
}

/* Status and Role Badges */
.admin-users__status-badge,
.admin-users__role-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: var(--extrasmallfont);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-users__status-badge--active {
  background-color: #dcfce7;
  color: #166534;
}

.admin-users__status-badge--inactive {
  background-color: #f3f4f6;
  color: #374151;
}

.admin-users__status-badge--suspended {
  background-color: #fee2e2;
  color: #dc2626;
}

.admin-users__status-badge--pending {
  background-color: #fef3c7;
  color: #d97706;
}

.admin-users__role-badge--admin {
  background-color: #ddd6fe;
  color: #7c3aed;
}

.admin-users__role-badge--seller {
  background-color: #dbeafe;
  color: #2563eb;
}

.admin-users__role-badge--buyer {
  background-color: #d1fae5;
  color: #059669;
}

/* Actions */
.admin-users__actions-cell {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
}

.admin-users__action-btn {
  background: none;
  border: 1px solid var(--light-gray);
  padding: 4px 8px;
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  cursor: pointer;
  transition: all 0.3s ease;
}

.admin-users__action-btn:hover {
  background-color: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
}

.admin-users__action-btn--danger {
  border-color: var(--btn-color);
  color: var(--btn-color);
}

.admin-users__action-btn--danger:hover {
  background-color: var(--btn-color);
  color: var(--white);
  border-color: var(--btn-color);
}

.admin-users__status-select {
  padding: 4px 8px;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  background-color: var(--white);
}

/* Pagination */
.admin-users__pagination {
  display: flex;
  justify-content: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-lg);
}

.admin-users__page-btn {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--light-gray);
  background-color: var(--white);
  color: var(--text-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
}

.admin-users__page-btn:hover:not(:disabled) {
  background-color: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
}

.admin-users__page-btn--active {
  background-color: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
}

.admin-users__page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Error */
.admin-users__error {
  background-color: #fee2e2;
  color: #dc2626;
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  margin-top: var(--spacing-lg);
}

/* User Details Modal */
.admin-users__user-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.admin-users__detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid var(--light-gray);
  font-size: var(--basefont);
}

.admin-users__detail-row:last-child {
  border-bottom: none;
}

.admin-users__detail-row strong {
  color: var(--text-color);
  font-weight: 600;
  min-width: 120px;
}

/* Mobile Responsive */
@media (max-width: 1024px) {
  .admin-users__table-container {
    overflow-x: auto;
  }

  .admin-users__table {
    min-width: 800px;
  }
}

@media (max-width: 768px) {
  .admin-users {
    padding: var(--spacing-md);
  }

  .admin-users__header {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .admin-users__filter-group {
    flex-direction: column;
    align-items: stretch;
  }

  .admin-users__search {
    min-width: auto;
  }

  .admin-users__stats {
    flex-wrap: wrap;
  }

  .admin-users__bulk-actions {
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: stretch;
  }
}

@media (max-width: 480px) {
  .admin-users {
    padding: var(--spacing-sm);
  }

  .admin-users__actions {
    flex-direction: column;
  }
}
