import React from "react";
import { CoolMode } from "../magicui/cool-mode";

export function CoolModeCustom() {
  return (
    <div className="relative justify-center" style={{ textAlign: 'center', padding: '20px' }}>
      <CoolMode
        options={{
          particle: "https://pbs.twimg.com/profile_images/1782811051504885763/YR5-kWOI_400x400.jpg",
        }}
      >
        <button
          style={{
            padding: '12px 24px',
            fontSize: '16px',
            fontWeight: 'bold',
            backgroundColor: '#EE3425',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            cursor: 'pointer',
            transition: 'all 0.2s ease'
          }}
          onMouseOver={(e) => e.target.style.backgroundColor = '#d12a1f'}
          onMouseOut={(e) => e.target.style.backgroundColor = '#EE3425'}
        >
          Click Me!
        </button>
      </CoolMode>
    </div>
  );
}

// Simple usage example with circle particles
export function CoolModeSimple() {
  return (
    <div style={{ textAlign: 'center', padding: '20px' }}>
      <CoolMode
        options={{
          particle: "circle",
        }}
      >
        <button
          style={{
            padding: '10px 20px',
            fontSize: '14px',
            backgroundColor: '#4ECDC4',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            cursor: 'pointer'
          }}
        >
          Simple Click
        </button>
      </CoolMode>
    </div>
  );
}
