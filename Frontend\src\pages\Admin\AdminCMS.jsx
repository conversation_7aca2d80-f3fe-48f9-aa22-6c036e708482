import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import Modal from '../../components/common/Modal';
import ConfirmDialog from '../../components/common/ConfirmDialog';
import { adminToasts } from '../../utils/toast';
import '../../styles/AdminCMS.css';

const AdminCMS = () => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('pages');
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);

  // Mock CMS data
  const [cmsData, setCmsData] = useState({
    pages: [
      {
        id: 1,
        title: 'Home Page',
        slug: 'home',
        status: 'published',
        lastModified: '2024-01-15T10:30:00Z',
        author: 'Admin',
        type: 'page'
      },
      {
        id: 2,
        title: 'About Us',
        slug: 'about',
        status: 'published',
        lastModified: '2024-01-14T15:20:00Z',
        author: 'Admin',
        type: 'page'
      },
      {
        id: 3,
        title: 'Contact',
        slug: 'contact',
        status: 'draft',
        lastModified: '2024-01-13T09:15:00Z',
        author: 'Admin',
        type: 'page'
      }
    ],
    components: [
      {
        id: 1,
        title: 'Header Navigation',
        type: 'navigation',
        status: 'active',
        lastModified: '2024-01-15T08:45:00Z',
        author: 'Admin'
      },
      {
        id: 2,
        title: 'Footer',
        type: 'footer',
        status: 'active',
        lastModified: '2024-01-14T16:30:00Z',
        author: 'Admin'
      },
      {
        id: 3,
        title: 'Hero Banner',
        type: 'banner',
        status: 'active',
        lastModified: '2024-01-13T11:20:00Z',
        author: 'Admin'
      }
    ],
    media: [
      {
        id: 1,
        title: 'Logo.png',
        type: 'image',
        size: '45 KB',
        dimensions: '200x80',
        uploadDate: '2024-01-15T12:00:00Z',
        url: '/assets/logo.png'
      },
      {
        id: 2,
        title: 'Hero-banner.jpg',
        type: 'image',
        size: '1.2 MB',
        dimensions: '1920x1080',
        uploadDate: '2024-01-14T14:30:00Z',
        url: '/assets/hero-banner.jpg'
      },
      {
        id: 3,
        title: 'About-video.mp4',
        type: 'video',
        size: '15.8 MB',
        duration: '2:30',
        uploadDate: '2024-01-13T10:15:00Z',
        url: '/assets/about-video.mp4'
      }
    ]
  });

  const handleEdit = (item) => {
    setSelectedItem(item);
    setShowEditModal(true);
  };

  const handleDelete = (item) => {
    setSelectedItem(item);
    setShowDeleteConfirm(true);
  };

  const confirmDelete = async () => {
    if (!selectedItem) return;

    try {
      setIsProcessing(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Remove item from state
      setCmsData(prev => ({
        ...prev,
        [activeTab]: prev[activeTab].filter(item => item.id !== selectedItem.id)
      }));

      adminToasts.success(`${selectedItem.title} deleted successfully`);
      setShowDeleteConfirm(false);
      setSelectedItem(null);
    } catch (error) {
      adminToasts.error('Failed to delete item');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleSave = async (updatedItem) => {
    try {
      setIsProcessing(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Update item in state
      setCmsData(prev => ({
        ...prev,
        [activeTab]: prev[activeTab].map(item => 
          item.id === updatedItem.id ? updatedItem : item
        )
      }));

      adminToasts.success(`${updatedItem.title} updated successfully`);
      setShowEditModal(false);
      setSelectedItem(null);
    } catch (error) {
      adminToasts.error('Failed to update item');
    } finally {
      setIsProcessing(false);
    }
  };

  const getStatusBadge = (status) => {
    const statusClasses = {
      published: 'admin-cms__status-badge--published',
      draft: 'admin-cms__status-badge--draft',
      active: 'admin-cms__status-badge--active',
      inactive: 'admin-cms__status-badge--inactive'
    };

    return (
      <span className={`admin-cms__status-badge ${statusClasses[status] || ''}`}>
        {status}
      </span>
    );
  };

  const renderTabContent = () => {
    const currentData = cmsData[activeTab] || [];

    if (currentData.length === 0) {
      return (
        <div className="admin-cms__empty">
          <p>No {activeTab} found</p>
          <button className="btn btn-primary" onClick={() => adminToasts.featureNotImplemented()}>
            Add New {activeTab.slice(0, -1)}
          </button>
        </div>
      );
    }

    return (
      <div className="admin-cms__table-container">
        <table className="admin-cms__table">
          <thead>
            <tr>
              <th>Title</th>
              {activeTab === 'pages' && <th>Slug</th>}
              {activeTab === 'components' && <th>Type</th>}
              {activeTab === 'media' && <th>Type</th>}
              {activeTab === 'media' && <th>Size</th>}
              <th>Status</th>
              <th>Last Modified</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {currentData.map((item) => (
              <tr key={item.id} className="admin-cms__table-row">
                <td>
                  <div className="admin-cms__item-info">
                    <div className="admin-cms__item-title">{item.title}</div>
                    {item.author && (
                      <div className="admin-cms__item-author">by {item.author}</div>
                    )}
                  </div>
                </td>
                {activeTab === 'pages' && (
                  <td>
                    <code className="admin-cms__slug">/{item.slug}</code>
                  </td>
                )}
                {(activeTab === 'components' || activeTab === 'media') && (
                  <td>
                    <span className="admin-cms__type">{item.type}</span>
                  </td>
                )}
                {activeTab === 'media' && (
                  <td>
                    <span className="admin-cms__size">{item.size}</span>
                    {item.dimensions && (
                      <div className="admin-cms__dimensions">{item.dimensions}</div>
                    )}
                    {item.duration && (
                      <div className="admin-cms__duration">{item.duration}</div>
                    )}
                  </td>
                )}
                <td>{getStatusBadge(item.status)}</td>
                <td>
                  <div className="admin-cms__date">
                    {new Date(item.lastModified || item.uploadDate).toLocaleDateString()}
                  </div>
                  <div className="admin-cms__time">
                    {new Date(item.lastModified || item.uploadDate).toLocaleTimeString()}
                  </div>
                </td>
                <td>
                  <div className="admin-cms__actions-cell">
                    <button
                      className="admin-cms__action-btn"
                      onClick={() => handleEdit(item)}
                    >
                      Edit
                    </button>
                    {activeTab === 'media' && (
                      <button
                        className="admin-cms__action-btn"
                        onClick={() => window.open(item.url, '_blank')}
                      >
                        View
                      </button>
                    )}
                    <button
                      className="admin-cms__action-btn admin-cms__action-btn--danger"
                      onClick={() => handleDelete(item)}
                    >
                      Delete
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  return (
    <div className="admin-cms">
      {/* Header */}
      <div className="admin-cms__header">
        <div className="admin-cms__title">
          <h1>Content Management System</h1>
          <p>Manage website pages, components, and media files</p>
        </div>
        <div className="admin-cms__actions">
          <button
            className="btn btn-outline"
            onClick={() => adminToasts.featureNotImplemented()}
          >
            Import
          </button>
          <button
            className="btn btn-outline"
            onClick={() => adminToasts.featureNotImplemented()}
          >
            Export
          </button>
          <button
            className="btn btn-primary"
            onClick={() => adminToasts.featureNotImplemented()}
          >
            Add New
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="admin-cms__tabs">
        <button
          className={`admin-cms__tab ${activeTab === 'pages' ? 'admin-cms__tab--active' : ''}`}
          onClick={() => setActiveTab('pages')}
        >
          Pages ({cmsData.pages.length})
        </button>
        <button
          className={`admin-cms__tab ${activeTab === 'components' ? 'admin-cms__tab--active' : ''}`}
          onClick={() => setActiveTab('components')}
        >
          Components ({cmsData.components.length})
        </button>
        <button
          className={`admin-cms__tab ${activeTab === 'media' ? 'admin-cms__tab--active' : ''}`}
          onClick={() => setActiveTab('media')}
        >
          Media ({cmsData.media.length})
        </button>
      </div>

      {/* Content */}
      <div className="admin-cms__content">
        {renderTabContent()}
      </div>

      {/* Edit Modal */}
      <Modal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        title={selectedItem ? `Edit ${selectedItem.title}` : 'Edit Item'}
        size="large"
      >
        {selectedItem && (
          <div className="admin-cms__edit-form">
            <p>Edit functionality for {selectedItem.title} will be implemented here.</p>
            <p>This would include rich text editors, media uploaders, and form fields specific to the content type.</p>
            <div className="admin-cms__edit-actions">
              <button
                className="btn btn-outline"
                onClick={() => setShowEditModal(false)}
              >
                Cancel
              </button>
              <button
                className="btn btn-primary"
                onClick={() => handleSave(selectedItem)}
                disabled={isProcessing}
              >
                {isProcessing ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
          </div>
        )}
      </Modal>

      {/* Delete Confirmation */}
      <ConfirmDialog
        isOpen={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        onConfirm={confirmDelete}
        title="Delete Item"
        message={`Are you sure you want to delete "${selectedItem?.title}"? This action cannot be undone.`}
        confirmText="Delete"
        type="danger"
        loading={isProcessing}
      />
    </div>
  );
};

export default AdminCMS;
