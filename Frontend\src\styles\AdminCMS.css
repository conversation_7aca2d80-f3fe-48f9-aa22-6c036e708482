/* Admin CMS Styles */
.admin-cms {
  padding: var(--spacing-lg);
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

/* Header */
.admin-cms__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--light-gray);
}

.admin-cms__title h1 {
  font-size: var(--heading2);
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: var(--spacing-xs);
}

.admin-cms__title p {
  color: var(--text-light);
  font-size: var(--basefont);
}

.admin-cms__actions {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}

/* Tabs */
.admin-cms__tabs {
  display: flex;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-xl);
  border-bottom: 1px solid var(--light-gray);
}

.admin-cms__tab {
  padding: var(--spacing-md) var(--spacing-lg);
  background: none;
  border: none;
  font-size: var(--basefont);
  font-weight: 500;
  color: var(--text-light);
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.admin-cms__tab:hover {
  color: var(--primary-color);
  background-color: var(--bg-gray);
}

.admin-cms__tab--active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
  background-color: var(--bg-gray);
}

/* Content */
.admin-cms__content {
  min-height: 400px;
}

/* Table */
.admin-cms__table-container {
  overflow-x: auto;
  border-radius: var(--border-radius);
  border: 1px solid var(--light-gray);
}

.admin-cms__table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--white);
}

.admin-cms__table th {
  background-color: var(--bg-gray);
  padding: var(--spacing-md);
  text-align: left;
  font-weight: 600;
  color: var(--text-color);
  border-bottom: 1px solid var(--light-gray);
  font-size: var(--smallfont);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-cms__table td {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--light-gray);
  vertical-align: top;
}

.admin-cms__table-row:hover {
  background-color: var(--bg-gray);
}

/* Item Info */
.admin-cms__item-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.admin-cms__item-title {
  font-weight: 600;
  color: var(--text-color);
  font-size: var(--basefont);
}

.admin-cms__item-author {
  font-size: var(--extrasmallfont);
  color: var(--text-light);
}

/* Slug */
.admin-cms__slug {
  background-color: var(--bg-gray);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: var(--extrasmallfont);
  color: var(--primary-color);
}

/* Type */
.admin-cms__type {
  background-color: var(--secondary-color);
  color: var(--white);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: var(--extrasmallfont);
  font-weight: 500;
  text-transform: capitalize;
}

/* Size and Dimensions */
.admin-cms__size {
  font-weight: 600;
  color: var(--text-color);
}

.admin-cms__dimensions,
.admin-cms__duration {
  font-size: var(--extrasmallfont);
  color: var(--text-light);
  margin-top: 2px;
}

/* Status Badges */
.admin-cms__status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: var(--extrasmallfont);
  font-weight: 500;
  text-transform: capitalize;
}

.admin-cms__status-badge--published {
  background-color: var(--success-color);
  color: var(--white);
}

.admin-cms__status-badge--draft {
  background-color: var(--warning-color);
  color: var(--white);
}

.admin-cms__status-badge--active {
  background-color: var(--success-color);
  color: var(--white);
}

.admin-cms__status-badge--inactive {
  background-color: var(--error-color);
  color: var(--white);
}

/* Date and Time */
.admin-cms__date {
  font-weight: 500;
  color: var(--text-color);
  font-size: var(--smallfont);
}

.admin-cms__time {
  font-size: var(--extrasmallfont);
  color: var(--text-light);
  margin-top: 2px;
}

/* Actions */
.admin-cms__actions-cell {
  display: flex;
  gap: var(--spacing-xs);
  align-items: center;
}

.admin-cms__action-btn {
  padding: 4px 8px;
  border: 1px solid var(--light-gray);
  background-color: var(--white);
  color: var(--text-color);
  border-radius: 4px;
  font-size: var(--extrasmallfont);
  cursor: pointer;
  transition: all 0.2s ease;
}

.admin-cms__action-btn:hover {
  background-color: var(--bg-gray);
  border-color: var(--primary-color);
}

.admin-cms__action-btn--danger {
  color: var(--error-color);
  border-color: var(--error-color);
}

.admin-cms__action-btn--danger:hover {
  background-color: var(--error-color);
  color: var(--white);
}

/* Empty State */
.admin-cms__empty {
  text-align: center;
  padding: var(--spacing-xl);
  color: var(--text-light);
}

.admin-cms__empty p {
  margin-bottom: var(--spacing-lg);
  font-size: var(--basefont);
}

/* Edit Form */
.admin-cms__edit-form {
  padding: var(--spacing-lg);
}

.admin-cms__edit-form p {
  margin-bottom: var(--spacing-md);
  color: var(--text-light);
}

.admin-cms__edit-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--light-gray);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .admin-cms__table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .admin-cms__table {
    min-width: 800px;
  }
}

@media (max-width: 768px) {
  .admin-cms {
    padding: var(--spacing-md);
  }

  .admin-cms__header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }

  .admin-cms__actions {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .admin-cms__tabs {
    flex-direction: column;
    gap: 0;
  }

  .admin-cms__tab {
    padding: var(--spacing-sm) var(--spacing-md);
    border-bottom: 1px solid var(--light-gray);
    border-right: none;
  }

  .admin-cms__tab--active {
    border-bottom-color: var(--light-gray);
    border-left: 3px solid var(--primary-color);
  }

  .admin-cms__table {
    min-width: 600px;
  }

  .admin-cms__table th,
  .admin-cms__table td {
    padding: var(--spacing-sm);
    font-size: var(--smallfont);
  }

  .admin-cms__actions-cell {
    flex-direction: column;
    gap: var(--spacing-xs);
    align-items: stretch;
  }

  .admin-cms__edit-actions {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
}

@media (max-width: 480px) {
  .admin-cms {
    padding: var(--spacing-sm);
  }

  .admin-cms__title h1 {
    font-size: var(--heading3);
  }

  .admin-cms__table {
    min-width: 500px;
  }

  .admin-cms__table th,
  .admin-cms__table td {
    padding: var(--spacing-xs);
  }

  .admin-cms__item-title {
    font-size: var(--smallfont);
  }

  /* Hide less important columns on small screens */
  .admin-cms__table th:nth-child(3),
  .admin-cms__table td:nth-child(3) {
    display: none;
  }
}

@media (max-width: 360px) {
  .admin-cms__table {
    min-width: 400px;
  }

  /* Hide more columns on extra small screens */
  .admin-cms__table th:nth-child(4),
  .admin-cms__table td:nth-child(4),
  .admin-cms__table th:nth-child(5),
  .admin-cms__table td:nth-child(5) {
    display: none;
  }
}
