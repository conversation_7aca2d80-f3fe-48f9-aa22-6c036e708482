/* Admin Analytics Styles */
.admin-analytics {
  padding: var(--spacing-lg);
}

.admin-analytics__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-xl);
}

.admin-analytics__title h1 {
  color: var(--text-color);
  font-size: var(--heading2);
  font-weight: 600;
  margin: 0 0 var(--spacing-sm) 0;
}

.admin-analytics__title p {
  color: var(--dark-gray);
  font-size: var(--basefont);
  margin: 0;
}

.admin-analytics__controls {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}

.admin-analytics__period-select {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  background-color: var(--white);
}

/* Loading State */
.admin-analytics__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  text-align: center;
}

.admin-analytics__spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--light-gray);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-md);
}

/* Key Metrics */
.admin-analytics__metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.admin-analytics__metric-card {
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  padding: var(--spacing-lg);
  box-shadow: var(--box-shadow-light);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.admin-analytics__metric-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

.admin-analytics__metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.admin-analytics__metric-header h3 {
  color: var(--dark-gray);
  font-size: var(--basefont);
  font-weight: 500;
  margin: 0;
}

.admin-analytics__metric-icon {
  font-size: var(--heading4);
}

.admin-analytics__metric-value {
  font-size: var(--heading2);
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: var(--spacing-sm);
}

.admin-analytics__metric-change {
  font-size: var(--smallfont);
  font-weight: 500;
}

.admin-analytics__metric-change.positive {
  color: #10b981;
}

.admin-analytics__metric-change.negative {
  color: #ef4444;
}

/* Charts Section */
.admin-analytics__charts {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.admin-analytics__chart-container {
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  padding: var(--spacing-lg);
  box-shadow: var(--box-shadow-light);
}

.admin-analytics__chart-header {
  margin-bottom: var(--spacing-lg);
}

.admin-analytics__chart-header h3 {
  color: var(--text-color);
  font-size: var(--heading5);
  font-weight: 600;
  margin: 0;
}

.admin-analytics__chart {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.admin-analytics__chart-placeholder {
  text-align: center;
  color: var(--dark-gray);
  width: 100%;
}

.admin-analytics__chart-placeholder p {
  margin: 0 0 var(--spacing-md) 0;
  font-size: var(--basefont);
}

/* Simple Bar Chart */
.admin-analytics__chart-data {
  display: flex;
  justify-content: space-around;
  align-items: end;
  height: 200px;
  margin-top: var(--spacing-lg);
}

.admin-analytics__chart-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
}

.admin-analytics__bar {
  width: 40px;
  background: linear-gradient(to top, var(--primary-color), var(--btn-color));
  border-radius: 4px 4px 0 0;
  min-height: 20px;
  transition: all 0.3s ease;
}

.admin-analytics__bar:hover {
  opacity: 0.8;
}

.admin-analytics__bar-label {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  font-weight: 500;
}

.admin-analytics__bar-value {
  font-size: var(--extrasmallfont);
  color: var(--text-color);
  font-weight: 600;
}

/* Growth Stats */
.admin-analytics__growth-stats {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

.admin-analytics__growth-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm);
  background-color: var(--bg-gray);
  border-radius: var(--border-radius);
}

.admin-analytics__growth-label {
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

.admin-analytics__growth-value {
  font-size: var(--basefont);
  font-weight: 600;
  color: var(--primary-color);
}

/* Performance Summary */
.admin-analytics__summary {
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  padding: var(--spacing-lg);
  box-shadow: var(--box-shadow-light);
}

.admin-analytics__summary-header {
  margin-bottom: var(--spacing-lg);
}

.admin-analytics__summary-header h3 {
  color: var(--text-color);
  font-size: var(--heading5);
  font-weight: 600;
  margin: 0;
}

.admin-analytics__summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.admin-analytics__summary-item h4 {
  color: var(--text-color);
  font-size: var(--basefont);
  font-weight: 600;
  margin: 0 0 var(--spacing-md) 0;
}

.admin-analytics__summary-item ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.admin-analytics__summary-item li {
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid var(--light-gray);
  color: var(--dark-gray);
  font-size: var(--smallfont);
}

.admin-analytics__summary-item li:last-child {
  border-bottom: none;
}

/* Mobile Responsive */
@media (max-width: 1024px) {
  .admin-analytics__charts {
    grid-template-columns: 1fr;
  }

  .admin-analytics__metrics {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .admin-analytics {
    padding: var(--spacing-md);
  }

  .admin-analytics__header {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .admin-analytics__metrics {
    grid-template-columns: 1fr;
  }

  .admin-analytics__summary-grid {
    grid-template-columns: 1fr;
  }

  .admin-analytics__chart-data {
    height: 150px;
  }

  .admin-analytics__bar {
    width: 30px;
  }
}

@media (max-width: 480px) {
  .admin-analytics {
    padding: var(--spacing-sm);
  }

  .admin-analytics__controls {
    flex-direction: column;
    width: 100%;
  }

  .admin-analytics__period-select {
    width: 100%;
  }
}
