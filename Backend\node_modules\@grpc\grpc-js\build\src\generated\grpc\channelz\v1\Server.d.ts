import type { ServerRef as _grpc_channelz_v1_ServerRef, ServerRef__Output as _grpc_channelz_v1_ServerRef__Output } from '../../../grpc/channelz/v1/ServerRef';
import type { ServerData as _grpc_channelz_v1_ServerData, ServerData__Output as _grpc_channelz_v1_ServerData__Output } from '../../../grpc/channelz/v1/ServerData';
import type { SocketRef as _grpc_channelz_v1_SocketRef, SocketRef__Output as _grpc_channelz_v1_SocketRef__Output } from '../../../grpc/channelz/v1/SocketRef';
/**
 * Server represents a single server.  There may be multiple servers in a single
 * program.
 */
export interface Server {
    /**
     * The identifier for a Server.  This should be set.
     */
    'ref'?: (_grpc_channelz_v1_ServerRef | null);
    /**
     * The associated data of the Server.
     */
    'data'?: (_grpc_channelz_v1_ServerData | null);
    /**
     * The sockets that the server is listening on.  There are no ordering
     * guarantees.  This may be absent.
     */
    'listen_socket'?: (_grpc_channelz_v1_SocketRef)[];
}
/**
 * Server represents a single server.  There may be multiple servers in a single
 * program.
 */
export interface Server__Output {
    /**
     * The identifier for a Server.  This should be set.
     */
    'ref': (_grpc_channelz_v1_ServerRef__Output | null);
    /**
     * The associated data of the Server.
     */
    'data': (_grpc_channelz_v1_ServerData__Output | null);
    /**
     * The sockets that the server is listening on.  There are no ordering
     * guarantees.  This may be absent.
     */
    'listen_socket': (_grpc_channelz_v1_SocketRef__Output)[];
}
