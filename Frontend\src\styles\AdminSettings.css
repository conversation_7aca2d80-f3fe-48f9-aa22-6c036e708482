/* Admin Settings Styles */
.admin-settings {
  padding: var(--spacing-lg);
}

.admin-settings__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-xl);
}

.admin-settings__title h1 {
  color: var(--text-color);
  font-size: var(--heading2);
  font-weight: 600;
  margin: 0 0 var(--spacing-sm) 0;
}

.admin-settings__title p {
  color: var(--dark-gray);
  font-size: var(--basefont);
  margin: 0;
}

.admin-settings__actions {
  display: flex;
  gap: var(--spacing-md);
}

/* Tabs */
.admin-settings__tabs {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-xl);
  border-bottom: 1px solid var(--light-gray);
}

.admin-settings__tab {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  background: none;
  border: none;
  border-bottom: 3px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--dark-gray);
  font-size: var(--basefont);
}

.admin-settings__tab:hover {
  color: var(--primary-color);
  background-color: var(--bg-gray);
}

.admin-settings__tab--active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
  background-color: var(--primary-light-color);
}

.admin-settings__tab-icon {
  font-size: var(--heading6);
}

.admin-settings__tab-label {
  font-weight: 500;
}

/* Content */
.admin-settings__content {
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  padding: var(--spacing-xl);
  box-shadow: var(--box-shadow-light);
}

.admin-settings__section h2 {
  color: var(--text-color);
  font-size: var(--heading4);
  font-weight: 600;
  margin: 0 0 var(--spacing-xl) 0;
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--light-gray);
}

/* Form Styles */
.admin-settings__form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.admin-settings__form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.admin-settings__form-group label {
  color: var(--text-color);
  font-size: var(--basefont);
  font-weight: 500;
}

.admin-settings__input,
.admin-settings__textarea,
.admin-settings__select {
  padding: var(--spacing-md);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  background-color: var(--white);
  transition: border-color 0.3s ease;
}

.admin-settings__input:focus,
.admin-settings__textarea:focus,
.admin-settings__select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(238, 52, 37, 0.1);
}

.admin-settings__textarea {
  resize: vertical;
  min-height: 80px;
}

.admin-settings__checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  font-size: var(--basefont);
  color: var(--text-color);
}

.admin-settings__checkbox {
  width: 18px;
  height: 18px;
  accent-color: var(--primary-color);
}

.admin-settings__help-text {
  color: var(--dark-gray);
  font-size: var(--smallfont);
  margin: 0;
  font-style: italic;
}

/* File Types */
.admin-settings__file-types {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background-color: var(--bg-gray);
  border-radius: var(--border-radius);
}

.admin-settings__file-types .admin-settings__checkbox-label {
  font-size: var(--smallfont);
  font-weight: 500;
}

/* Mobile Responsive */
@media (max-width: 1024px) {
  .admin-settings__tabs {
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  .admin-settings {
    padding: var(--spacing-md);
  }

  .admin-settings__header {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .admin-settings__tabs {
    flex-direction: column;
    border-bottom: none;
    background-color: var(--bg-gray);
    border-radius: var(--border-radius);
    padding: var(--spacing-sm);
  }

  .admin-settings__tab {
    border-bottom: none;
    border-radius: var(--border-radius);
    justify-content: flex-start;
  }

  .admin-settings__tab--active {
    background-color: var(--primary-color);
    color: var(--white);
  }

  .admin-settings__content {
    padding: var(--spacing-lg);
  }

  .admin-settings__file-types {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .admin-settings {
    padding: var(--spacing-sm);
  }

  .admin-settings__actions {
    flex-direction: column;
    width: 100%;
  }

  .admin-settings__content {
    padding: var(--spacing-md);
  }

  .admin-settings__file-types {
    grid-template-columns: 1fr;
  }
}
