import React, { useState, useRef, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { logout } from '../../redux/slices/authSlice';
import '../../styles/AdminHeader.css';

const AdminHeader = ({ onToggleSidebar, sidebarCollapsed, isMobile }) => {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [notificationsOpen, setNotificationsOpen] = useState(false);
  const [imageError, setImageError] = useState(false);
  const dropdownRef = useRef(null);
  const notificationsRef = useRef(null);

  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { user } = useSelector((state) => state.auth);
  const { dashboardStats } = useSelector((state) => state.admin);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setDropdownOpen(false);
      }
      if (notificationsRef.current && !notificationsRef.current.contains(event.target)) {
        setNotificationsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleLogout = () => {
    // Temporarily disabled for development
    // dispatch(logout());
    // navigate('/auth');
    console.log('Logout clicked - temporarily disabled');
  };

  const toggleDropdown = () => {
    setDropdownOpen(!dropdownOpen);
    setNotificationsOpen(false);
  };

  const toggleNotifications = () => {
    setNotificationsOpen(!notificationsOpen);
    setDropdownOpen(false);
  };

  // Mock notifications data
  const notifications = [
    {
      id: 1,
      type: 'user',
      message: 'New user registration',
      time: '2 minutes ago',
      unread: true
    },
    {
      id: 2,
      type: 'content',
      message: 'Content pending approval',
      time: '5 minutes ago',
      unread: true
    },
    {
      id: 3,
      type: 'order',
      message: 'New order received',
      time: '10 minutes ago',
      unread: false
    }
  ];

  const unreadCount = notifications.filter(n => n.unread).length;

  return (
    <header className="admin-header">
      <div className="admin-header__left">
        {/* Sidebar Toggle */}
        <button
          className="admin-header__toggle"
          onClick={onToggleSidebar}
          aria-label="Toggle sidebar"
        >
          <span className="admin-header__toggle-icon">
            {isMobile ? '☰' : (sidebarCollapsed ? '☰' : '✕')}
          </span>
        </button>

        {/* Page Title */}
        <div className="admin-header__title">
          <h1>Admin Dashboard</h1>
        </div>
      </div>

      <div className="admin-header__right">
        {/* Quick Stats */}
        <div className="admin-header__stats">
          <div className="admin-header__stat">
            <span className="admin-header__stat-label">Pending</span>
            <span className="admin-header__stat-value">
              {dashboardStats.pendingApprovals || 0}
            </span>
          </div>
        </div>

        {/* Notifications */}
        <div className="admin-header__notifications" ref={notificationsRef}>
          <button
            className="admin-header__notifications-btn"
            onClick={toggleNotifications}
            aria-label="Notifications"
          >
            <span className="admin-header__notifications-icon">🔔</span>
            {unreadCount > 0 && (
              <span className="admin-header__notifications-badge">
                {unreadCount}
              </span>
            )}
          </button>

          {notificationsOpen && (
            <div className="admin-header__notifications-dropdown">
              <div className="admin-header__notifications-header">
                <h3>Notifications</h3>
                <span className="admin-header__notifications-count">
                  {unreadCount} unread
                </span>
              </div>
              <div className="admin-header__notifications-list">
                {notifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`admin-header__notification-item ${
                      notification.unread ? 'admin-header__notification-item--unread' : ''
                    }`}
                  >
                    <div className="admin-header__notification-content">
                      <p>{notification.message}</p>
                      <span className="admin-header__notification-time">
                        {notification.time}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
              <div className="admin-header__notifications-footer">
                <button className="admin-header__notifications-view-all">
                  View All Notifications
                </button>
              </div>
            </div>
          )}
        </div>

        {/* User Menu */}
        <div className="admin-header__user" ref={dropdownRef}>
          <button
            className="admin-header__user-btn"
            onClick={toggleDropdown}
            aria-label="User menu"
          >
            {!imageError && user?.avatar ? (
              <img
                src={user.avatar}
                alt={user?.name || 'Admin'}
                className="admin-header__user-avatar"
                onError={() => setImageError(true)}
              />
            ) : (
              <div className="admin-header__user-avatar-fallback">
                <span>👤</span>
              </div>
            )}
            <span className="admin-header__user-name">
              {user?.name || 'Admin User'}
            </span>
            <span className="admin-header__user-arrow">▼</span>
          </button>

          {dropdownOpen && (
            <div className="admin-header__user-dropdown">
              <div className="admin-header__user-info">
                <p className="admin-header__user-email">{user?.email || '<EMAIL>'}</p>
                <p className="admin-header__user-role">Administrator</p>
              </div>
              <div className="admin-header__user-menu">
                <button
                  className="admin-header__user-menu-item"
                  onClick={() => navigate('/admin/profile')}
                >
                  <span>👤</span>
                  Profile
                </button>
                <button
                  className="admin-header__user-menu-item"
                  onClick={() => navigate('/admin/settings')}
                >
                  <span>⚙️</span>
                  Settings
                </button>
                <hr className="admin-header__user-menu-divider" />
                <button
                  className="admin-header__user-menu-item admin-header__user-menu-item--danger"
                  onClick={handleLogout}
                >
                  <span>🚪</span>
                  Logout
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </header>
  );
};

export default AdminHeader;
