/* Confirm Dialog Styles */
.confirm-dialog {
  text-align: center;
  padding: var(--spacing-lg);
}

.confirm-dialog__icon {
  font-size: 48px;
  margin-bottom: var(--spacing-lg);
}

.confirm-dialog__content {
  margin-bottom: var(--spacing-xl);
}

.confirm-dialog__title {
  color: var(--text-color);
  font-size: var(--heading5);
  font-weight: 600;
  margin: 0 0 var(--spacing-md) 0;
}

.confirm-dialog__message {
  color: var(--dark-gray);
  font-size: var(--basefont);
  margin: 0;
  line-height: 1.5;
}

.confirm-dialog__actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
}

.confirm-dialog__actions .btn {
  min-width: 100px;
}

.confirm-dialog__spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: var(--spacing-sm);
}

/* Type variants */
.confirm-dialog--danger .confirm-dialog__title {
  color: var(--btn-color);
}

.confirm-dialog--warning .confirm-dialog__title {
  color: #d97706;
}

.confirm-dialog--success .confirm-dialog__title {
  color: #059669;
}

/* Button variants */
.btn-danger {
  background-color: var(--btn-color);
  color: var(--white);
  border: 1px solid var(--btn-color);
}

.btn-danger:hover {
  background-color: #dc2626;
  border-color: #dc2626;
}

.btn-danger:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Mobile Responsive */
@media (max-width: 480px) {
  .confirm-dialog__actions {
    flex-direction: column;
  }

  .confirm-dialog__actions .btn {
    width: 100%;
  }
}
