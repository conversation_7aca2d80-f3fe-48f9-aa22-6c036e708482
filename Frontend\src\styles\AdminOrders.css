/* Admin Orders Styles */
.admin-orders {
  padding: var(--spacing-lg);
}

.admin-orders__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-lg);
}

.admin-orders__title h1 {
  color: var(--text-color);
  font-size: var(--heading2);
  font-weight: 600;
  margin: 0 0 var(--spacing-sm) 0;
}

.admin-orders__title p {
  color: var(--dark-gray);
  font-size: var(--basefont);
  margin: 0;
}

.admin-orders__actions {
  display: flex;
  gap: var(--spacing-md);
}

/* Loading State */
.admin-orders__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  text-align: center;
}

.admin-orders__spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--light-gray);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-md);
}

/* Filters */
.admin-orders__filters {
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--box-shadow-light);
}

.admin-orders__filter-group {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
  flex-wrap: wrap;
}

.admin-orders__search {
  flex: 1;
  min-width: 200px;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
}

.admin-orders__select {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  background-color: var(--white);
}

/* Stats */
.admin-orders__stats {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.admin-orders__stat {
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  padding: var(--spacing-lg);
  box-shadow: var(--box-shadow-light);
  text-align: center;
  min-width: 120px;
  flex: 1;
}

.admin-orders__stat-value {
  display: block;
  font-size: var(--heading3);
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: var(--spacing-xs);
}

.admin-orders__stat-label {
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

/* Bulk Actions */
.admin-orders__bulk-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--primary-light-color);
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-lg);
}

.admin-orders__bulk-buttons {
  display: flex;
  gap: var(--spacing-sm);
}

/* Table */
.admin-orders__table-container {
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow-light);
  overflow: hidden;
  margin-bottom: var(--spacing-lg);
}

.admin-orders__table {
  width: 100%;
  border-collapse: collapse;
}

.admin-orders__table th,
.admin-orders__table td {
  padding: var(--spacing-md);
  text-align: left;
  border-bottom: 1px solid var(--light-gray);
}

.admin-orders__table th {
  background-color: var(--bg-gray);
  font-weight: 600;
  color: var(--text-color);
  font-size: var(--smallfont);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-orders__table-row:hover {
  background-color: var(--bg-gray);
}

.admin-orders__order-number {
  font-weight: 600;
  color: var(--primary-color);
  font-size: var(--basefont);
}

.admin-orders__buyer-info {
  max-width: 200px;
}

.admin-orders__buyer-name {
  font-weight: 600;
  color: var(--text-color);
  font-size: var(--basefont);
  margin-bottom: 2px;
}

.admin-orders__buyer-email {
  color: var(--dark-gray);
  font-size: var(--smallfont);
}

.admin-orders__content-info {
  max-width: 250px;
}

.admin-orders__content-title {
  font-weight: 500;
  color: var(--text-color);
  font-size: var(--smallfont);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.admin-orders__amount {
  font-weight: 700;
  color: var(--primary-color);
  font-size: var(--basefont);
}

.admin-orders__date-info {
  min-width: 100px;
}

.admin-orders__created-date {
  font-weight: 500;
  color: var(--text-color);
  font-size: var(--smallfont);
  margin-bottom: 2px;
}

.admin-orders__created-time {
  color: var(--dark-gray);
  font-size: var(--extrasmallfont);
}

/* Status Badges */
.admin-orders__status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: var(--extrasmallfont);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-orders__status-badge--completed {
  background-color: #dcfce7;
  color: #166534;
}

.admin-orders__status-badge--pending {
  background-color: #fef3c7;
  color: #d97706;
}

.admin-orders__status-badge--processing {
  background-color: #dbeafe;
  color: #2563eb;
}

.admin-orders__status-badge--cancelled {
  background-color: #fee2e2;
  color: #dc2626;
}

.admin-orders__status-badge--refunded {
  background-color: #f3e8ff;
  color: #7c3aed;
}

/* Actions */
.admin-orders__actions-cell {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
  flex-wrap: wrap;
}

.admin-orders__action-btn {
  background: none;
  border: 1px solid var(--light-gray);
  padding: 4px 8px;
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  cursor: pointer;
  transition: all 0.3s ease;
}

.admin-orders__action-btn:hover {
  background-color: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
}

.admin-orders__action-btn--refund {
  border-color: var(--btn-color);
  color: var(--btn-color);
}

.admin-orders__action-btn--refund:hover {
  background-color: var(--btn-color);
  color: var(--white);
}

/* Pagination */
.admin-orders__pagination {
  display: flex;
  justify-content: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-lg);
}

.admin-orders__page-btn {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--light-gray);
  background-color: var(--white);
  color: var(--text-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
}

.admin-orders__page-btn:hover:not(:disabled) {
  background-color: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
}

.admin-orders__page-btn--active {
  background-color: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
}

.admin-orders__page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Error */
.admin-orders__error {
  background-color: #fee2e2;
  color: #dc2626;
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  margin-top: var(--spacing-lg);
}

/* Order Details Modal */
.admin-orders__order-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.admin-orders__detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid var(--light-gray);
  font-size: var(--basefont);
}

.admin-orders__detail-row:last-child {
  border-bottom: none;
}

.admin-orders__detail-row strong {
  color: var(--text-color);
  font-weight: 600;
  min-width: 140px;
}

/* Mobile Responsive */
@media (max-width: 1024px) {
  .admin-orders__table-container {
    overflow-x: auto;
  }

  .admin-orders__table {
    min-width: 1000px;
  }

  .admin-orders__stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .admin-orders {
    padding: var(--spacing-md);
  }

  .admin-orders__header {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .admin-orders__filter-group {
    flex-direction: column;
    align-items: stretch;
  }

  .admin-orders__search {
    min-width: auto;
  }

  .admin-orders__stats {
    flex-wrap: wrap;
  }

  .admin-orders__bulk-actions {
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: stretch;
  }
}

@media (max-width: 480px) {
  .admin-orders {
    padding: var(--spacing-sm);
  }

  .admin-orders__actions {
    flex-direction: column;
  }

  .admin-orders__stats {
    flex-direction: column;
  }
}
