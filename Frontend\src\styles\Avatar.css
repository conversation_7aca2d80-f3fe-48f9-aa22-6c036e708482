/* Avatar Component Styles */
.avatar {
  position: relative;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-gray);
  flex-shrink: 0;
}

.avatar__image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.avatar__fallback {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary-light-color);
  color: var(--primary-color);
  font-weight: 500;
}

/* Size variants */
.avatar--small {
  width: 32px;
  height: 32px;
}

.avatar--small .avatar__fallback {
  font-size: 14px;
}

.avatar--medium {
  width: 40px;
  height: 40px;
}

.avatar--medium .avatar__fallback {
  font-size: 18px;
}

.avatar--large {
  width: 48px;
  height: 48px;
}

.avatar--large .avatar__fallback {
  font-size: 24px;
}
