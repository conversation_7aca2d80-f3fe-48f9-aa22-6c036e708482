import { Link, useLocation, useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import "../../styles/Navbar.css";
import logo from "../../assets/images/XOsports-hub-logo.svg";
import Sidebar from "./Sidebar";
import { RiMenu5Line } from "react-icons/ri";
import AccountDropdown from "../buyer/AccountDropdown";
import { logout, getUserForNavbar } from "../../redux/slices/authSlice";
import { CoolMode } from "../magicui/cool-mode";

const Navbar = () => {
  const location = useLocation();
  const path = location.pathname;
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [userData, setUserData] = useState(null);
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // Get auth state from Redux
  const { user, isAuthenticated } = useSelector((state) => state.auth);

  // Fetch user data on component mount if authenticated
  useEffect(() => {
    const token = localStorage.getItem('xosportshub_token');
    if (token && !user) {
      dispatch(getUserForNavbar());
    }
  }, [dispatch, user]);

  // Update user data when auth state changes
  useEffect(() => {
    if (isAuthenticated && user) {
      setUserData(user);
      console.log("User data from API:", user);
    } else {
      setUserData(null);
      console.log("No user data found");
    }
  }, [user, isAuthenticated]);

  // Determine user role based on authentication state and user data
  let userRole = "visitor";
  if (userData && userData.role) {
    userRole = userData.role;
  } else if (path.startsWith("/buyer")) {
    userRole = "buyer";
  } else if (path.startsWith("/seller")) {
    userRole = "seller";
  }

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  // Handle logout
  const handleLogout = () => {
    dispatch(logout());
    navigate("/");
  };

  return (
    <>
      <nav className="navbar-component navbar">
        <div className="navbar-container max-container">
          <div className="navbar-logo">
            <CoolMode
              options={{
                particle: "sports",
              }}
            >
              <Link to="/">
                <img src={logo} alt="XO Sports Hub Logo" />
              </Link>
            </CoolMode>
          </div>

          {/* Hamburger menu for mobile */}
          <button className="navbar-toggle" onClick={toggleSidebar}>
            {sidebarOpen ? "✕" : <RiMenu5Line />}
          </button>

          {/* Desktop navigation links */}
          <div className="navbar-links">
            {userRole === "visitor" && (
              // Visitor links
              <>
                <Link to="/" className={path === "/" ? "active" : ""}>
                  Home
                </Link>
                <Link
                  to="/buyer/dashboard"
                  className={path === "/buyer/dashboard" ? "active" : ""}
                >
                  Buy
                </Link>
                <Link
                  to="/seller/dashboard"
                  className={path === "/seller/dashboard" ? "active" : ""}
                >
                  Sell
                </Link>
                <Link to="/info" className={path === "/info" ? "active" : ""}>
                  About Us
                </Link>
                <Link
                  to="/contact"
                  className={path === "/contact" ? "active" : ""}
                >
                  Contact
                </Link>
              </>
            )}

            {userRole === "buyer" && (
              // Buyer links
              <>
                <Link to="/" className={path === "/" ? "active" : ""}>
                  Home
                </Link>
                <Link
                  to="/buyer/dashboard"
                  className={path === "/buyer/dashboard" ? "active" : ""}
                >
                  Buy
                </Link>
                <Link
                  to="/seller/dashboard"
                  className={path === "/seller/dashboard" ? "active" : ""}
                >
                  Sell
                </Link>
                <Link to="/info" className={path === "/info" ? "active" : ""}>
                  About Us
                </Link>
                <Link
                  to="/contact"
                  className={path === "/contact" ? "active" : ""}
                >
                  Contact
                </Link>
              </>
            )}

            {userRole === "seller" && (
              // Seller links
              <>
                <Link to="/" className={path === "/" ? "active" : ""}>
                  Home
                </Link>
                <Link
                  to="/buyer/dashboard"
                  className={path === "/buyer/dashboard" ? "active" : ""}
                >
                  Buy
                </Link>
                <Link
                  to="/seller/dashboard"
                  className={path === "/seller/dashboard" ? "active" : ""}
                >
                  Sell
                </Link>
                <Link to="/info" className={path === "/info" ? "active" : ""}>
                  About Us
                </Link>
                <Link
                  to="/contact"
                  className={path === "/contact" ? "active" : ""}
                >
                  Contact
                </Link>
              </>
            )}
          </div>

          {/* Desktop auth buttons */}
          <div className="navbar-auth">
            {userRole === "visitor" && (
              <>
                <Link to="/auth" className="btn signinbtn">
                  Sign In
                </Link>
                <Link to="/signup" className="btn signupbtn">
                  Sign Up
                </Link>
              </>
            )}

            {userRole === "buyer" && <AccountDropdown />}

            {userRole === "seller" && (
              <button className="btn btn-outline" onClick={handleLogout}>
                Logout
              </button>
            )}

            {userRole === "admin" && (
              <button className="btn btn-outline" onClick={handleLogout}>
                Logout
              </button>
            )}
          </div>
        </div>
      </nav>

      {/* Mobile Sidebar */}
      <Sidebar
        isOpen={sidebarOpen}
        toggleSidebar={toggleSidebar}
        userRole={userRole}
      />
    </>
  );
};

export default Navbar;
