import React from 'react';
import { Link } from 'react-router-dom';

const AdminTest = () => {
  return (
    <div style={{ padding: '20px' }}>
      <h1>Admin Panel Test Page</h1>
      <p>This is a test page to verify that the admin panel is working correctly.</p>

      <div style={{ marginTop: '20px' }}>
        <h2>Available Admin Routes:</h2>
        <ul>
          <li><Link to="/admin/dashboard">Dashboard</Link></li>
          <li><Link to="/admin/users">Users Management</Link></li>
          <li><Link to="/admin/content">Content Management</Link></li>
          <li><Link to="/admin/orders">Orders Management</Link></li>
          <li><Link to="/admin/analytics">Analytics Dashboard</Link></li>
          <li><Link to="/admin/settings">System Settings</Link></li>
        </ul>
      </div>

      <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#f0f0f0', borderRadius: '5px' }}>
        <h3>Features Implemented:</h3>
        <ul>
          <li>✅ Admin Layout with Sidebar and Header</li>
          <li>✅ Dashboard with Stats Cards</li>
          <li>✅ Users Management with Table</li>
          <li>✅ Content Management with Filtering</li>
          <li>✅ Orders Management with Search</li>
          <li>✅ Redux State Management</li>
          <li>✅ Mock Data for Development</li>
          <li>✅ Debounced Search (prevents request loops)</li>
          <li>✅ Responsive Design</li>
          <li>✅ CSS Variables from index.css</li>
        </ul>
      </div>

      <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#e8f5e8', borderRadius: '5px' }}>
        <h3>To Access Admin Panel:</h3>
        <ol>
          <li>Make sure you're logged in as an admin user</li>
          <li>Navigate to <code>/admin/dashboard</code></li>
          <li>Use the sidebar to navigate between sections</li>
        </ol>
      </div>
    </div>
  );
};

export default AdminTest;
