import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import Modal from '../../components/common/Modal';
import ConfirmDialog from '../../components/common/ConfirmDialog';
import { adminToasts } from '../../utils/toast';
import {
  fetchAllContent,
  updateContentStatus,
  updateFilter,
  clearFilters,
  toggleSelectItem,
  selectAllItems,
  clearSelectedItems
} from '../../redux/slices/adminSlice';
import '../../styles/AdminContent.css';

const AdminContent = () => {
  const dispatch = useDispatch();
  const { content, loading, error, filters, selectedItems } = useSelector((state) => state.admin);
  const [currentPage, setCurrentPage] = useState(1);
  const [showFilters, setShowFilters] = useState(false);

  // Modal states
  const [selectedContent, setSelectedContent] = useState(null);
  const [showContentModal, setShowContentModal] = useState(false);
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [contentToDelete, setContentToDelete] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [approvalAction, setApprovalAction] = useState(null); // 'approve' or 'reject'

  useEffect(() => {
    dispatch(fetchAllContent({
      page: currentPage,
      ...filters.content
    }));
  }, [dispatch, currentPage, filters.content]);

  const handleFilterChange = (filterName, value) => {
    dispatch(updateFilter({ section: 'content', filterName, value }));
    setCurrentPage(1);
  };

  const handleStatusUpdate = (contentId, status) => {
    dispatch(updateContentStatus({ contentId, status }));
  };

  const handleSelectContent = (contentId) => {
    dispatch(toggleSelectItem({ section: 'content', itemId: contentId }));
  };

  const handleSelectAll = () => {
    const allContentIds = content.data.map(item => item.id);
    if (selectedItems.content.length === allContentIds.length) {
      dispatch(clearSelectedItems({ section: 'content' }));
    } else {
      dispatch(selectAllItems({ section: 'content', itemIds: allContentIds }));
    }
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const getStatusBadge = (status) => {
    const statusClasses = {
      approved: 'admin-content__status-badge--approved',
      pending: 'admin-content__status-badge--pending',
      rejected: 'admin-content__status-badge--rejected',
      draft: 'admin-content__status-badge--draft'
    };

    return (
      <span className={`admin-content__status-badge ${statusClasses[status] || ''}`}>
        {status}
      </span>
    );
  };

  const getCategoryBadge = (category) => {
    return (
      <span className="admin-content__category-badge">
        {category}
      </span>
    );
  };

  // Interactive functions
  const handleStatusUpdate = async (contentId, newStatus) => {
    const contentItem = content.data.find(c => c.id === contentId);
    try {
      setIsProcessing(true);
      await dispatch(updateContentStatus({ contentId, status: newStatus })).unwrap();
      if (newStatus === 'approved') {
        adminToasts.contentApproved(contentItem?.title || 'Content');
      } else if (newStatus === 'rejected') {
        adminToasts.contentRejected(contentItem?.title || 'Content');
      }
    } catch (error) {
      adminToasts.error('Failed to update content status');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleViewContent = (contentItem) => {
    setSelectedContent(contentItem);
    setShowContentModal(true);
  };

  const handleEditContent = (contentItem) => {
    setSelectedContent(contentItem);
    setShowContentModal(true);
    adminToasts.featureNotImplemented();
  };

  const handleApproveContent = (contentItem) => {
    setSelectedContent(contentItem);
    setApprovalAction('approve');
    setShowApprovalModal(true);
  };

  const handleRejectContent = (contentItem) => {
    setSelectedContent(contentItem);
    setApprovalAction('reject');
    setShowApprovalModal(true);
  };

  const confirmApprovalAction = async () => {
    if (!selectedContent || !approvalAction) return;

    try {
      setIsProcessing(true);
      await handleStatusUpdate(selectedContent.id, approvalAction === 'approve' ? 'approved' : 'rejected');
      setShowApprovalModal(false);
      setSelectedContent(null);
      setApprovalAction(null);
    } catch (error) {
      adminToasts.error('Failed to update content status');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDeleteContent = (contentItem) => {
    setContentToDelete(contentItem);
    setShowDeleteConfirm(true);
  };

  const confirmDeleteContent = async () => {
    if (!contentToDelete) return;

    try {
      setIsProcessing(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      adminToasts.contentDeleted(contentToDelete.title);
      setShowDeleteConfirm(false);
      setContentToDelete(null);
    } catch (error) {
      adminToasts.error('Failed to delete content');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleBulkApprove = async () => {
    if (selectedItems.content.length === 0) {
      adminToasts.validationError('selection');
      return;
    }

    try {
      setIsProcessing(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      adminToasts.bulkActionCompleted(selectedItems.content.length, 'Approved');
      dispatch(clearSelectedItems({ section: 'content' }));
    } catch (error) {
      adminToasts.error('Failed to approve content');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleBulkReject = async () => {
    if (selectedItems.content.length === 0) {
      adminToasts.validationError('selection');
      return;
    }

    try {
      setIsProcessing(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      adminToasts.bulkActionCompleted(selectedItems.content.length, 'Rejected');
      dispatch(clearSelectedItems({ section: 'content' }));
    } catch (error) {
      adminToasts.error('Failed to reject content');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleExportContent = () => {
    adminToasts.exportStarted();
    setTimeout(() => {
      const csvContent = "data:text/csv;charset=utf-8," +
        "Title,Category,Seller,Price,Status,Downloads,Created\n" +
        content.data.map(item =>
          `"${item.title}","${item.category}","${item.seller.name}","${item.price}","${item.status}","${item.downloads}","${new Date(item.createdAt).toLocaleDateString()}"`
        ).join("\n");

      const encodedUri = encodeURI(csvContent);
      const link = document.createElement("a");
      link.setAttribute("href", encodedUri);
      link.setAttribute("download", "content_export.csv");
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      adminToasts.success('Content exported successfully');
    }, 2000);
  };

  if (loading.content) {
    return (
      <div className="admin-content">
        <div className="admin-content__loading">
          <div className="admin-content__spinner"></div>
          <p>Loading content...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="admin-content">
      {/* Header */}
      <div className="admin-content__header">
        <div className="admin-content__title">
          <h1>Content Management</h1>
          <p>Manage all content, approve submissions, and monitor performance</p>
        </div>
        <div className="admin-content__actions">
          <button
            className="btn btn-outline"
            onClick={() => setShowFilters(!showFilters)}
          >
            Filters
          </button>
          <button className="btn btn-primary">
            Add Content
          </button>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="admin-content__filters">
          <div className="admin-content__filter-group">
            <input
              type="text"
              placeholder="Search content..."
              value={filters.content.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className="admin-content__search"
            />
            <select
              value={filters.content.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="admin-content__select"
            >
              <option value="">All Status</option>
              <option value="approved">Approved</option>
              <option value="pending">Pending</option>
              <option value="rejected">Rejected</option>
              <option value="draft">Draft</option>
            </select>
            <select
              value={filters.content.category}
              onChange={(e) => handleFilterChange('category', e.target.value)}
              className="admin-content__select"
            >
              <option value="">All Categories</option>
              <option value="Basketball">Basketball</option>
              <option value="Football">Football</option>
              <option value="Soccer">Soccer</option>
              <option value="Baseball">Baseball</option>
              <option value="Tennis">Tennis</option>
            </select>
            <button
              className="btn btn-outline"
              onClick={() => dispatch(clearFilters({ section: 'content' }))}
            >
              Clear
            </button>
          </div>
        </div>
      )}

      {/* Stats */}
      <div className="admin-content__stats">
        <div className="admin-content__stat">
          <span className="admin-content__stat-value">{content.totalContent}</span>
          <span className="admin-content__stat-label">Total Content</span>
        </div>
        <div className="admin-content__stat">
          <span className="admin-content__stat-value">{selectedItems.content.length}</span>
          <span className="admin-content__stat-label">Selected</span>
        </div>
        <div className="admin-content__stat">
          <span className="admin-content__stat-value">
            {content.data.filter(item => item.status === 'pending').length}
          </span>
          <span className="admin-content__stat-label">Pending</span>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedItems.content.length > 0 && (
        <div className="admin-content__bulk-actions">
          <span>Selected {selectedItems.content.length} items</span>
          <div className="admin-content__bulk-buttons">
            <button className="btn btn-outline btn-sm">Approve</button>
            <button className="btn btn-outline btn-sm">Reject</button>
            <button className="btn btn-outline btn-sm">Delete</button>
          </div>
        </div>
      )}

      {/* Content Table */}
      <div className="admin-content__table-container">
        <table className="admin-content__table">
          <thead>
            <tr>
              <th>
                <input
                  type="checkbox"
                  checked={selectedItems.content.length === content.data.length && content.data.length > 0}
                  onChange={handleSelectAll}
                />
              </th>
              <th>Content</th>
              <th>Category</th>
              <th>Seller</th>
              <th>Price</th>
              <th>Status</th>
              <th>Downloads</th>
              <th>Created</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {content.data.map((item) => (
              <tr key={item.id} className="admin-content__table-row">
                <td>
                  <input
                    type="checkbox"
                    checked={selectedItems.content.includes(item.id)}
                    onChange={() => handleSelectContent(item.id)}
                  />
                </td>
                <td>
                  <div className="admin-content__content-info">
                    <div className="admin-content__title">{item.title}</div>
                    <div className="admin-content__description">{item.description}</div>
                  </div>
                </td>
                <td>{getCategoryBadge(item.category)}</td>
                <td>
                  <div className="admin-content__seller-info">
                    <div className="admin-content__seller-name">{item.seller.name}</div>
                  </div>
                </td>
                <td>
                  <span className="admin-content__price">${item.price}</span>
                </td>
                <td>{getStatusBadge(item.status)}</td>
                <td>
                  <span className="admin-content__downloads">{item.downloads}</span>
                </td>
                <td>{new Date(item.createdAt).toLocaleDateString()}</td>
                <td>
                  <div className="admin-content__actions-cell">
                    <button className="admin-content__action-btn">View</button>
                    <button className="admin-content__action-btn">Edit</button>
                    <select
                      value={item.status}
                      onChange={(e) => handleStatusUpdate(item.id, e.target.value)}
                      className="admin-content__status-select"
                    >
                      <option value="approved">Approved</option>
                      <option value="pending">Pending</option>
                      <option value="rejected">Rejected</option>
                    </select>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {content.totalPages > 1 && (
        <div className="admin-content__pagination">
          <button
            className="admin-content__page-btn"
            disabled={currentPage === 1}
            onClick={() => handlePageChange(currentPage - 1)}
          >
            Previous
          </button>

          {Array.from({ length: content.totalPages }, (_, i) => i + 1).map((page) => (
            <button
              key={page}
              className={`admin-content__page-btn ${currentPage === page ? 'admin-content__page-btn--active' : ''}`}
              onClick={() => handlePageChange(page)}
            >
              {page}
            </button>
          ))}

          <button
            className="admin-content__page-btn"
            disabled={currentPage === content.totalPages}
            onClick={() => handlePageChange(currentPage + 1)}
          >
            Next
          </button>
        </div>
      )}

      {error.content && (
        <div className="admin-content__error">
          <p>Error: {error.content}</p>
        </div>
      )}
    </div>
  );
};

export default AdminContent;
