import React from 'react';
import Modal from './Modal';
import '../../styles/ConfirmDialog.css';

const ConfirmDialog = ({
  isOpen,
  onClose,
  onConfirm,
  title = 'Confirm Action',
  message = 'Are you sure you want to proceed?',
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  type = 'default', // default, danger, warning, success
  loading = false
}) => {
  const handleConfirm = () => {
    onConfirm();
    if (!loading) {
      onClose();
    }
  };

  const getTypeClass = () => {
    switch (type) {
      case 'danger':
        return 'confirm-dialog--danger';
      case 'warning':
        return 'confirm-dialog--warning';
      case 'success':
        return 'confirm-dialog--success';
      default:
        return 'confirm-dialog--default';
    }
  };

  const getIcon = () => {
    switch (type) {
      case 'danger':
        return '⚠️';
      case 'warning':
        return '⚠️';
      case 'success':
        return '✅';
      default:
        return '❓';
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="small"
      showCloseButton={false}
      closeOnOverlayClick={!loading}
    >
      <div className={`confirm-dialog ${getTypeClass()}`}>
        <div className="confirm-dialog__icon">
          {getIcon()}
        </div>
        
        <div className="confirm-dialog__content">
          <h3 className="confirm-dialog__title">{title}</h3>
          <p className="confirm-dialog__message">{message}</p>
        </div>
        
        <div className="confirm-dialog__actions">
          <button
            className="btn btn-outline"
            onClick={onClose}
            disabled={loading}
          >
            {cancelText}
          </button>
          <button
            className={`btn ${type === 'danger' ? 'btn-danger' : 'btn-primary'}`}
            onClick={handleConfirm}
            disabled={loading}
          >
            {loading ? (
              <>
                <span className="confirm-dialog__spinner"></span>
                Processing...
              </>
            ) : (
              confirmText
            )}
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default ConfirmDialog;
