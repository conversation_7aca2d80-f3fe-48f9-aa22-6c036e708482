/* Admin Content Styles */
.admin-content {
  padding: var(--spacing-lg);
}

.admin-content__header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-lg);
}

.admin-content__title h1 {
  color: var(--text-color);
  font-size: var(--heading2);
  font-weight: 600;
  margin: 0 0 var(--spacing-sm) 0;
}

.admin-content__title p {
  color: var(--dark-gray);
  font-size: var(--basefont);
  margin: 0;
}

.admin-content__actions {
  display: flex;
  gap: var(--spacing-md);
}

/* Loading State */
.admin-content__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  text-align: center;
}

.admin-content__spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--light-gray);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-md);
}

/* Filters */
.admin-content__filters {
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--box-shadow-light);
}

.admin-content__filter-group {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
  flex-wrap: wrap;
}

.admin-content__search {
  flex: 1;
  min-width: 200px;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
}

.admin-content__select {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  background-color: var(--white);
}

/* Stats */
.admin-content__stats {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.admin-content__stat {
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  padding: var(--spacing-lg);
  box-shadow: var(--box-shadow-light);
  text-align: center;
  min-width: 120px;
}

.admin-content__stat-value {
  display: block;
  font-size: var(--heading3);
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: var(--spacing-xs);
}

.admin-content__stat-label {
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

/* Bulk Actions */
.admin-content__bulk-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--primary-light-color);
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-lg);
}

.admin-content__bulk-buttons {
  display: flex;
  gap: var(--spacing-sm);
}

/* Table */
.admin-content__table-container {
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow-light);
  overflow: hidden;
  margin-bottom: var(--spacing-lg);
}

.admin-content__table {
  width: 100%;
  border-collapse: collapse;
}

.admin-content__table th,
.admin-content__table td {
  padding: var(--spacing-md);
  text-align: left;
  border-bottom: 1px solid var(--light-gray);
}

.admin-content__table th {
  background-color: var(--bg-gray);
  font-weight: 600;
  color: var(--text-color);
  font-size: var(--smallfont);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-content__table-row:hover {
  background-color: var(--bg-gray);
}

.admin-content__content-info {
  max-width: 300px;
}

.admin-content__title {
  font-weight: 600;
  color: var(--text-color);
  font-size: var(--basefont);
  margin-bottom: 4px;
}

.admin-content__description {
  color: var(--dark-gray);
  font-size: var(--smallfont);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.admin-content__seller-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.admin-content__seller-name {
  font-weight: 500;
  color: var(--text-color);
  font-size: var(--smallfont);
}

.admin-content__price {
  font-weight: 600;
  color: var(--primary-color);
  font-size: var(--basefont);
}

.admin-content__downloads {
  font-weight: 500;
  color: var(--text-color);
}

/* Status and Category Badges */
.admin-content__status-badge,
.admin-content__category-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: var(--extrasmallfont);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.admin-content__status-badge--approved {
  background-color: #dcfce7;
  color: #166534;
}

.admin-content__status-badge--pending {
  background-color: #fef3c7;
  color: #d97706;
}

.admin-content__status-badge--rejected {
  background-color: #fee2e2;
  color: #dc2626;
}

.admin-content__status-badge--draft {
  background-color: #f3f4f6;
  color: #374151;
}

.admin-content__category-badge {
  background-color: #dbeafe;
  color: #2563eb;
}

/* Actions */
.admin-content__actions-cell {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
  flex-wrap: wrap;
}

.admin-content__action-btn {
  background: none;
  border: 1px solid var(--light-gray);
  padding: 4px 8px;
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  cursor: pointer;
  transition: all 0.3s ease;
}

.admin-content__action-btn:hover {
  background-color: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
}

.admin-content__action-btn--approve {
  border-color: #059669;
  color: #059669;
}

.admin-content__action-btn--approve:hover {
  background-color: #059669;
  color: var(--white);
  border-color: #059669;
}

.admin-content__action-btn--reject {
  border-color: #d97706;
  color: #d97706;
}

.admin-content__action-btn--reject:hover {
  background-color: #d97706;
  color: var(--white);
  border-color: #d97706;
}

.admin-content__status-select {
  padding: 4px 8px;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  background-color: var(--white);
}

/* Pagination */
.admin-content__pagination {
  display: flex;
  justify-content: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-lg);
}

.admin-content__page-btn {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--light-gray);
  background-color: var(--white);
  color: var(--text-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
}

.admin-content__page-btn:hover:not(:disabled) {
  background-color: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
}

.admin-content__page-btn--active {
  background-color: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
}

.admin-content__page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Error */
.admin-content__error {
  background-color: #fee2e2;
  color: #dc2626;
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  margin-top: var(--spacing-lg);
}

/* Content Details Modal */
.admin-content__content-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.admin-content__detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid var(--light-gray);
  font-size: var(--basefont);
}

.admin-content__detail-row:last-child {
  border-bottom: none;
}

.admin-content__detail-row strong {
  color: var(--text-color);
  font-weight: 600;
  min-width: 120px;
}

/* Tablet Responsive */
@media (max-width: 1024px) {
  .admin-content__table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .admin-content__table {
    min-width: 1100px;
  }

  .admin-content__stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .admin-content {
    padding: var(--spacing-md);
  }

  .admin-content__header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }

  .admin-content__actions {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .admin-content__filter-group {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm);
  }

  .admin-content__search {
    min-width: auto;
    width: 100%;
  }

  .admin-content__select {
    width: 100%;
  }

  .admin-content__stats {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .admin-content__bulk-actions {
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: stretch;
  }

  .admin-content__bulk-buttons {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  /* Mobile table improvements */
  .admin-content__table {
    min-width: 800px;
  }

  .admin-content__table th,
  .admin-content__table td {
    padding: var(--spacing-sm);
    font-size: var(--smallfont);
  }

  .admin-content__content-info {
    max-width: 200px;
  }

  .admin-content__actions-cell {
    flex-direction: column;
    gap: var(--spacing-xs);
    align-items: stretch;
  }

  .admin-content__action-btn {
    font-size: var(--extrasmallfont);
    padding: 4px 8px;
  }

  .admin-content__status-select {
    font-size: var(--extrasmallfont);
    padding: 4px 8px;
  }
}

/* Small Mobile */
@media (max-width: 480px) {
  .admin-content {
    padding: var(--spacing-sm);
  }

  .admin-content__title h1 {
    font-size: var(--heading3);
  }

  .admin-content__stats {
    gap: var(--spacing-sm);
  }

  .admin-content__stat {
    padding: var(--spacing-md);
  }

  .admin-content__table {
    min-width: 600px;
  }

  .admin-content__table th,
  .admin-content__table td {
    padding: var(--spacing-xs);
  }

  .admin-content__content-info {
    max-width: 150px;
  }

  .admin-content__title,
  .admin-content__description {
    font-size: var(--extrasmallfont);
  }

  /* Hide less important columns on very small screens */
  .admin-content__table th:nth-child(4),
  .admin-content__table td:nth-child(4) {
    display: none;
  }
}

/* Extra small screens */
@media (max-width: 360px) {
  .admin-content__table {
    min-width: 500px;
  }

  /* Hide more columns on extra small screens */
  .admin-content__table th:nth-child(3),
  .admin-content__table td:nth-child(3),
  .admin-content__table th:nth-child(5),
  .admin-content__table td:nth-child(5) {
    display: none;
  }
}
