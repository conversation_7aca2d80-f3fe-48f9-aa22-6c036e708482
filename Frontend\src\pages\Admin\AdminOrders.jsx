import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { 
  fetchAllOrders, 
  updateFilter, 
  clearFilters,
  toggleSelectItem,
  selectAllItems,
  clearSelectedItems
} from '../../redux/slices/adminSlice';
import '../../styles/AdminOrders.css';

const AdminOrders = () => {
  const dispatch = useDispatch();
  const { orders, loading, error, filters, selectedItems } = useSelector((state) => state.admin);
  const [currentPage, setCurrentPage] = useState(1);
  const [showFilters, setShowFilters] = useState(false);

  useEffect(() => {
    dispatch(fetchAllOrders({ 
      page: currentPage, 
      ...filters.orders 
    }));
  }, [dispatch, currentPage, filters.orders]);

  const handleFilterChange = (filterName, value) => {
    dispatch(updateFilter({ section: 'orders', filterName, value }));
    setCurrentPage(1);
  };

  const handleSelectOrder = (orderId) => {
    dispatch(toggleSelectItem({ section: 'orders', itemId: orderId }));
  };

  const handleSelectAll = () => {
    const allOrderIds = orders.data.map(order => order.id);
    if (selectedItems.orders.length === allOrderIds.length) {
      dispatch(clearSelectedItems({ section: 'orders' }));
    } else {
      dispatch(selectAllItems({ section: 'orders', itemIds: allOrderIds }));
    }
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const getStatusBadge = (status) => {
    const statusClasses = {
      completed: 'admin-orders__status-badge--completed',
      pending: 'admin-orders__status-badge--pending',
      cancelled: 'admin-orders__status-badge--cancelled',
      refunded: 'admin-orders__status-badge--refunded',
      processing: 'admin-orders__status-badge--processing'
    };

    return (
      <span className={`admin-orders__status-badge ${statusClasses[status] || ''}`}>
        {status}
      </span>
    );
  };

  const formatAmount = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  if (loading.orders) {
    return (
      <div className="admin-orders">
        <div className="admin-orders__loading">
          <div className="admin-orders__spinner"></div>
          <p>Loading orders...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="admin-orders">
      {/* Header */}
      <div className="admin-orders__header">
        <div className="admin-orders__title">
          <h1>Order Management</h1>
          <p>Monitor all orders, transactions, and customer purchases</p>
        </div>
        <div className="admin-orders__actions">
          <button 
            className="btn btn-outline"
            onClick={() => setShowFilters(!showFilters)}
          >
            Filters
          </button>
          <button className="btn btn-primary">
            Export Orders
          </button>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="admin-orders__filters">
          <div className="admin-orders__filter-group">
            <input
              type="text"
              placeholder="Search orders..."
              value={filters.orders.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className="admin-orders__search"
            />
            <select
              value={filters.orders.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="admin-orders__select"
            >
              <option value="">All Status</option>
              <option value="completed">Completed</option>
              <option value="pending">Pending</option>
              <option value="processing">Processing</option>
              <option value="cancelled">Cancelled</option>
              <option value="refunded">Refunded</option>
            </select>
            <select
              value={filters.orders.dateRange}
              onChange={(e) => handleFilterChange('dateRange', e.target.value)}
              className="admin-orders__select"
            >
              <option value="">All Time</option>
              <option value="today">Today</option>
              <option value="week">This Week</option>
              <option value="month">This Month</option>
              <option value="quarter">This Quarter</option>
            </select>
            <button 
              className="btn btn-outline"
              onClick={() => dispatch(clearFilters({ section: 'orders' }))}
            >
              Clear
            </button>
          </div>
        </div>
      )}

      {/* Stats */}
      <div className="admin-orders__stats">
        <div className="admin-orders__stat">
          <span className="admin-orders__stat-value">{orders.totalOrders}</span>
          <span className="admin-orders__stat-label">Total Orders</span>
        </div>
        <div className="admin-orders__stat">
          <span className="admin-orders__stat-value">{selectedItems.orders.length}</span>
          <span className="admin-orders__stat-label">Selected</span>
        </div>
        <div className="admin-orders__stat">
          <span className="admin-orders__stat-value">
            {orders.data.filter(order => order.status === 'pending').length}
          </span>
          <span className="admin-orders__stat-label">Pending</span>
        </div>
        <div className="admin-orders__stat">
          <span className="admin-orders__stat-value">
            {formatAmount(orders.data.reduce((sum, order) => sum + order.amount, 0))}
          </span>
          <span className="admin-orders__stat-label">Total Value</span>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedItems.orders.length > 0 && (
        <div className="admin-orders__bulk-actions">
          <span>Selected {selectedItems.orders.length} orders</span>
          <div className="admin-orders__bulk-buttons">
            <button className="btn btn-outline btn-sm">Mark Complete</button>
            <button className="btn btn-outline btn-sm">Cancel</button>
            <button className="btn btn-outline btn-sm">Refund</button>
          </div>
        </div>
      )}

      {/* Orders Table */}
      <div className="admin-orders__table-container">
        <table className="admin-orders__table">
          <thead>
            <tr>
              <th>
                <input
                  type="checkbox"
                  checked={selectedItems.orders.length === orders.data.length && orders.data.length > 0}
                  onChange={handleSelectAll}
                />
              </th>
              <th>Order #</th>
              <th>Buyer</th>
              <th>Content</th>
              <th>Amount</th>
              <th>Status</th>
              <th>Date</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {orders.data.map((order) => (
              <tr key={order.id} className="admin-orders__table-row">
                <td>
                  <input
                    type="checkbox"
                    checked={selectedItems.orders.includes(order.id)}
                    onChange={() => handleSelectOrder(order.id)}
                  />
                </td>
                <td>
                  <div className="admin-orders__order-number">
                    {order.orderNumber}
                  </div>
                </td>
                <td>
                  <div className="admin-orders__buyer-info">
                    <div className="admin-orders__buyer-name">{order.buyer.name}</div>
                    <div className="admin-orders__buyer-email">{order.buyer.email}</div>
                  </div>
                </td>
                <td>
                  <div className="admin-orders__content-info">
                    <div className="admin-orders__content-title">{order.content.title}</div>
                  </div>
                </td>
                <td>
                  <span className="admin-orders__amount">{formatAmount(order.amount)}</span>
                </td>
                <td>{getStatusBadge(order.status)}</td>
                <td>
                  <div className="admin-orders__date-info">
                    <div className="admin-orders__created-date">
                      {new Date(order.createdAt).toLocaleDateString()}
                    </div>
                    <div className="admin-orders__created-time">
                      {new Date(order.createdAt).toLocaleTimeString()}
                    </div>
                  </div>
                </td>
                <td>
                  <div className="admin-orders__actions-cell">
                    <button className="admin-orders__action-btn">View</button>
                    <button className="admin-orders__action-btn">Invoice</button>
                    {order.status === 'completed' && (
                      <button className="admin-orders__action-btn admin-orders__action-btn--refund">
                        Refund
                      </button>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {orders.totalPages > 1 && (
        <div className="admin-orders__pagination">
          <button
            className="admin-orders__page-btn"
            disabled={currentPage === 1}
            onClick={() => handlePageChange(currentPage - 1)}
          >
            Previous
          </button>
          
          {Array.from({ length: orders.totalPages }, (_, i) => i + 1).map((page) => (
            <button
              key={page}
              className={`admin-orders__page-btn ${currentPage === page ? 'admin-orders__page-btn--active' : ''}`}
              onClick={() => handlePageChange(page)}
            >
              {page}
            </button>
          ))}
          
          <button
            className="admin-orders__page-btn"
            disabled={currentPage === orders.totalPages}
            onClick={() => handlePageChange(currentPage + 1)}
          >
            Next
          </button>
        </div>
      )}

      {error.orders && (
        <div className="admin-orders__error">
          <p>Error: {error.orders}</p>
        </div>
      )}
    </div>
  );
};

export default AdminOrders;
