import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import Modal from '../../components/common/Modal';
import ConfirmDialog from '../../components/common/ConfirmDialog';
import { adminToasts } from '../../utils/toast';
import {
  fetchAllOrders,
  updateFilter,
  clearFilters,
  toggleSelectItem,
  selectAllItems,
  clearSelectedItems
} from '../../redux/slices/adminSlice';
import '../../styles/AdminOrders.css';

const AdminOrders = () => {
  const dispatch = useDispatch();
  const { orders, loading, error, filters, selectedItems } = useSelector((state) => state.admin);
  const [currentPage, setCurrentPage] = useState(1);
  const [showFilters, setShowFilters] = useState(false);

  // Modal states
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [showOrderModal, setShowOrderModal] = useState(false);
  const [showRefundConfirm, setShowRefundConfirm] = useState(false);
  const [orderToRefund, setOrderToRefund] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    dispatch(fetchAllOrders({
      page: currentPage,
      ...filters.orders
    }));
  }, [dispatch, currentPage, filters.orders]);

  const handleFilterChange = (filterName, value) => {
    dispatch(updateFilter({ section: 'orders', filterName, value }));
    setCurrentPage(1);
  };

  const handleSelectOrder = (orderId) => {
    dispatch(toggleSelectItem({ section: 'orders', itemId: orderId }));
  };

  const handleSelectAll = () => {
    const allOrderIds = orders.data.map(order => order.id);
    if (selectedItems.orders.length === allOrderIds.length) {
      dispatch(clearSelectedItems({ section: 'orders' }));
    } else {
      dispatch(selectAllItems({ section: 'orders', itemIds: allOrderIds }));
    }
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const getStatusBadge = (status) => {
    const statusClasses = {
      completed: 'admin-orders__status-badge--completed',
      pending: 'admin-orders__status-badge--pending',
      cancelled: 'admin-orders__status-badge--cancelled',
      refunded: 'admin-orders__status-badge--refunded',
      processing: 'admin-orders__status-badge--processing'
    };

    return (
      <span className={`admin-orders__status-badge ${statusClasses[status] || ''}`}>
        {status}
      </span>
    );
  };

  const formatAmount = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Interactive functions
  const handleViewOrder = (order) => {
    setSelectedOrder(order);
    setShowOrderModal(true);
  };

  const handleRefundOrder = (order) => {
    setOrderToRefund(order);
    setShowRefundConfirm(true);
  };

  const confirmRefund = async () => {
    if (!orderToRefund) return;

    try {
      setIsProcessing(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      adminToasts.orderRefunded(orderToRefund.orderNumber);
      setShowRefundConfirm(false);
      setOrderToRefund(null);
    } catch (error) {
      adminToasts.error('Failed to process refund');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleBulkStatusChange = async (status) => {
    if (selectedItems.orders.length === 0) {
      adminToasts.validationError('selection');
      return;
    }

    try {
      setIsProcessing(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      adminToasts.bulkActionCompleted(selectedItems.orders.length, `Status changed to ${status}`);
      dispatch(clearSelectedItems({ section: 'orders' }));
    } catch (error) {
      adminToasts.error('Failed to update order status');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleExportOrders = () => {
    adminToasts.exportStarted();
    setTimeout(() => {
      const csvContent = "data:text/csv;charset=utf-8," +
        "Order Number,Buyer,Content,Amount,Status,Date\n" +
        orders.data.map(order =>
          `"${order.orderNumber}","${order.buyer.name}","${order.content.title}","${order.amount}","${order.status}","${new Date(order.createdAt).toLocaleDateString()}"`
        ).join("\n");

      const encodedUri = encodeURI(csvContent);
      const link = document.createElement("a");
      link.setAttribute("href", encodedUri);
      link.setAttribute("download", "orders_export.csv");
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      adminToasts.success('Orders exported successfully');
    }, 2000);
  };

  const handleGenerateInvoice = (order) => {
    adminToasts.info(`Generating invoice for order ${order.orderNumber}...`);
    setTimeout(() => {
      adminToasts.success('Invoice generated successfully');
    }, 1000);
  };

  if (loading.orders) {
    return (
      <div className="admin-orders">
        <div className="admin-orders__loading">
          <div className="admin-orders__spinner"></div>
          <p>Loading orders...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="admin-orders">
      {/* Header */}
      <div className="admin-orders__header">
        <div className="admin-orders__title">
          <h1>Order Management</h1>
          <p>Monitor all orders, transactions, and customer purchases</p>
        </div>
        <div className="admin-orders__actions">
          <button
            className="btn btn-outline"
            onClick={() => setShowFilters(!showFilters)}
          >
            Filters
          </button>
          <button
            className="btn btn-primary"
            onClick={handleExportOrders}
          >
            Export Orders
          </button>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="admin-orders__filters">
          <div className="admin-orders__filter-group">
            <input
              type="text"
              placeholder="Search orders..."
              value={filters.orders.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className="admin-orders__search"
            />
            <select
              value={filters.orders.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="admin-orders__select"
            >
              <option value="">All Status</option>
              <option value="completed">Completed</option>
              <option value="pending">Pending</option>
              <option value="processing">Processing</option>
              <option value="cancelled">Cancelled</option>
              <option value="refunded">Refunded</option>
            </select>
            <select
              value={filters.orders.dateRange}
              onChange={(e) => handleFilterChange('dateRange', e.target.value)}
              className="admin-orders__select"
            >
              <option value="">All Time</option>
              <option value="today">Today</option>
              <option value="week">This Week</option>
              <option value="month">This Month</option>
              <option value="quarter">This Quarter</option>
            </select>
            <button
              className="btn btn-outline"
              onClick={() => dispatch(clearFilters({ section: 'orders' }))}
            >
              Clear
            </button>
          </div>
        </div>
      )}

      {/* Stats */}
      <div className="admin-orders__stats">
        <div className="admin-orders__stat">
          <span className="admin-orders__stat-value">{orders.totalOrders}</span>
          <span className="admin-orders__stat-label">Total Orders</span>
        </div>
        <div className="admin-orders__stat">
          <span className="admin-orders__stat-value">{selectedItems.orders.length}</span>
          <span className="admin-orders__stat-label">Selected</span>
        </div>
        <div className="admin-orders__stat">
          <span className="admin-orders__stat-value">
            {orders.data.filter(order => order.status === 'pending').length}
          </span>
          <span className="admin-orders__stat-label">Pending</span>
        </div>
        <div className="admin-orders__stat">
          <span className="admin-orders__stat-value">
            {formatAmount(orders.data.reduce((sum, order) => sum + order.amount, 0))}
          </span>
          <span className="admin-orders__stat-label">Total Value</span>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedItems.orders.length > 0 && (
        <div className="admin-orders__bulk-actions">
          <span>Selected {selectedItems.orders.length} orders</span>
          <div className="admin-orders__bulk-buttons">
            <button
              className="btn btn-outline btn-sm"
              onClick={() => handleBulkStatusChange('completed')}
              disabled={isProcessing}
            >
              Mark Complete
            </button>
            <button
              className="btn btn-outline btn-sm"
              onClick={() => handleBulkStatusChange('cancelled')}
              disabled={isProcessing}
            >
              Cancel
            </button>
            <button
              className="btn btn-outline btn-sm"
              onClick={() => handleBulkStatusChange('refunded')}
              disabled={isProcessing}
            >
              Refund
            </button>
          </div>
        </div>
      )}

      {/* Orders Table */}
      <div className="admin-orders__table-container">
        <table className="admin-orders__table">
          <thead>
            <tr>
              <th>
                <input
                  type="checkbox"
                  checked={selectedItems.orders.length === orders.data.length && orders.data.length > 0}
                  onChange={handleSelectAll}
                />
              </th>
              <th>Order #</th>
              <th>Buyer</th>
              <th>Content</th>
              <th>Amount</th>
              <th>Status</th>
              <th>Date</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {orders.data.map((order) => (
              <tr key={order.id} className="admin-orders__table-row">
                <td>
                  <input
                    type="checkbox"
                    checked={selectedItems.orders.includes(order.id)}
                    onChange={() => handleSelectOrder(order.id)}
                  />
                </td>
                <td>
                  <div className="admin-orders__order-number">
                    {order.orderNumber}
                  </div>
                </td>
                <td>
                  <div className="admin-orders__buyer-info">
                    <div className="admin-orders__buyer-name">{order.buyer.name}</div>
                    <div className="admin-orders__buyer-email">{order.buyer.email}</div>
                  </div>
                </td>
                <td>
                  <div className="admin-orders__content-info">
                    <div className="admin-orders__content-title">{order.content.title}</div>
                  </div>
                </td>
                <td>
                  <span className="admin-orders__amount">{formatAmount(order.amount)}</span>
                </td>
                <td>{getStatusBadge(order.status)}</td>
                <td>
                  <div className="admin-orders__date-info">
                    <div className="admin-orders__created-date">
                      {new Date(order.createdAt).toLocaleDateString()}
                    </div>
                    <div className="admin-orders__created-time">
                      {new Date(order.createdAt).toLocaleTimeString()}
                    </div>
                  </div>
                </td>
                <td>
                  <div className="admin-orders__actions-cell">
                    <button
                      className="admin-orders__action-btn"
                      onClick={() => handleViewOrder(order)}
                    >
                      View
                    </button>
                    <button
                      className="admin-orders__action-btn"
                      onClick={() => handleGenerateInvoice(order)}
                    >
                      Invoice
                    </button>
                    {order.status === 'completed' && (
                      <button
                        className="admin-orders__action-btn admin-orders__action-btn--refund"
                        onClick={() => handleRefundOrder(order)}
                      >
                        Refund
                      </button>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {orders.totalPages > 1 && (
        <div className="admin-orders__pagination">
          <button
            className="admin-orders__page-btn"
            disabled={currentPage === 1}
            onClick={() => handlePageChange(currentPage - 1)}
          >
            Previous
          </button>

          {Array.from({ length: orders.totalPages }, (_, i) => i + 1).map((page) => (
            <button
              key={page}
              className={`admin-orders__page-btn ${currentPage === page ? 'admin-orders__page-btn--active' : ''}`}
              onClick={() => handlePageChange(page)}
            >
              {page}
            </button>
          ))}

          <button
            className="admin-orders__page-btn"
            disabled={currentPage === orders.totalPages}
            onClick={() => handlePageChange(currentPage + 1)}
          >
            Next
          </button>
        </div>
      )}

      {error.orders && (
        <div className="admin-orders__error">
          <p>Error: {error.orders}</p>
        </div>
      )}

      {/* Order Details Modal */}
      <Modal
        isOpen={showOrderModal}
        onClose={() => setShowOrderModal(false)}
        title={selectedOrder ? `Order Details - ${selectedOrder.orderNumber}` : 'Order Details'}
        size="large"
      >
        {selectedOrder && (
          <div className="admin-orders__order-details">
            <div className="admin-orders__detail-row">
              <strong>Order Number:</strong> {selectedOrder.orderNumber}
            </div>
            <div className="admin-orders__detail-row">
              <strong>Buyer:</strong> {selectedOrder.buyer.name} ({selectedOrder.buyer.email})
            </div>
            <div className="admin-orders__detail-row">
              <strong>Content:</strong> {selectedOrder.content.title}
            </div>
            <div className="admin-orders__detail-row">
              <strong>Amount:</strong> {formatAmount(selectedOrder.amount)}
            </div>
            <div className="admin-orders__detail-row">
              <strong>Status:</strong> {selectedOrder.status}
            </div>
            <div className="admin-orders__detail-row">
              <strong>Order Date:</strong> {new Date(selectedOrder.createdAt).toLocaleDateString()}
            </div>
            <div className="admin-orders__detail-row">
              <strong>Payment Method:</strong> Credit Card
            </div>
            <div className="admin-orders__detail-row">
              <strong>Transaction ID:</strong> TXN-{selectedOrder.id}-{Date.now()}
            </div>
          </div>
        )}
      </Modal>

      {/* Refund Confirmation */}
      <ConfirmDialog
        isOpen={showRefundConfirm}
        onClose={() => setShowRefundConfirm(false)}
        onConfirm={confirmRefund}
        title="Process Refund"
        message={`Are you sure you want to refund order "${orderToRefund?.orderNumber}" for ${formatAmount(orderToRefund?.amount || 0)}?`}
        confirmText="Process Refund"
        type="warning"
        loading={isProcessing}
      />
    </div>
  );
};

export default AdminOrders;
