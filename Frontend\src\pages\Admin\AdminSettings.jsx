import React, { useState } from 'react';
import ConfirmDialog from '../../components/common/ConfirmDialog';
import { adminToasts } from '../../utils/toast';
import '../../styles/AdminSettings.css';

const AdminSettings = () => {
  const [settings, setSettings] = useState({
    siteName: 'XOsportshub',
    siteDescription: 'Your premier sports content marketplace',
    contactEmail: '<EMAIL>',
    supportEmail: '<EMAIL>',
    maintenanceMode: false,
    userRegistration: true,
    sellerApproval: true,
    contentModeration: true,
    emailNotifications: true,
    smsNotifications: false,
    commissionRate: 15,
    minWithdrawal: 50,
    maxFileSize: 100,
    allowedFileTypes: ['pdf', 'doc', 'docx', 'mp4', 'mp3'],
    currency: 'USD',
    timezone: 'America/New_York'
  });

  const [activeTab, setActiveTab] = useState('general');
  const [showResetConfirm, setShowResetConfirm] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  const handleInputChange = (field, value) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));
    setHasChanges(true);
  };

  const handleSave = async () => {
    try {
      setIsProcessing(true);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      adminToasts.settingsSaved();
      setHasChanges(false);
    } catch (error) {
      adminToasts.error('Failed to save settings');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleReset = () => {
    setShowResetConfirm(true);
  };

  const confirmReset = async () => {
    try {
      setIsProcessing(true);
      // Reset to default values
      setSettings({
        siteName: 'XOsportshub',
        siteDescription: 'Your premier sports content marketplace',
        contactEmail: '<EMAIL>',
        supportEmail: '<EMAIL>',
        maintenanceMode: false,
        userRegistration: true,
        sellerApproval: true,
        contentModeration: true,
        emailNotifications: true,
        smsNotifications: false,
        commissionRate: 15,
        minWithdrawal: 50,
        maxFileSize: 100,
        allowedFileTypes: ['pdf', 'doc', 'docx', 'mp4', 'mp3'],
        currency: 'USD',
        timezone: 'America/New_York'
      });
      setHasChanges(false);
      setShowResetConfirm(false);
      adminToasts.success('Settings reset to defaults');
    } catch (error) {
      adminToasts.error('Failed to reset settings');
    } finally {
      setIsProcessing(false);
    }
  };

  const tabs = [
    { id: 'general', label: 'General', icon: '⚙️' },
    { id: 'users', label: 'Users', icon: '👥' },
    { id: 'content', label: 'Content', icon: '📄' },
    { id: 'payments', label: 'Payments', icon: '💳' },
    { id: 'notifications', label: 'Notifications', icon: '🔔' }
  ];

  return (
    <div className="admin-settings">
      {/* Header */}
      <div className="admin-settings__header">
        <div className="admin-settings__title">
          <h1>System Settings</h1>
          <p>Configure your platform settings and preferences</p>
        </div>
        <div className="admin-settings__actions">
          <button
            className="btn btn-outline"
            onClick={handleReset}
            disabled={isProcessing}
          >
            Reset to Defaults
          </button>
          <button
            className={`btn ${hasChanges ? 'btn-primary' : 'btn-outline'}`}
            onClick={handleSave}
            disabled={isProcessing || !hasChanges}
          >
            {isProcessing ? 'Saving...' : 'Save Changes'}
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="admin-settings__tabs">
        {tabs.map(tab => (
          <button
            key={tab.id}
            className={`admin-settings__tab ${activeTab === tab.id ? 'admin-settings__tab--active' : ''}`}
            onClick={() => setActiveTab(tab.id)}
          >
            <span className="admin-settings__tab-icon">{tab.icon}</span>
            <span className="admin-settings__tab-label">{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Content */}
      <div className="admin-settings__content">
        {/* General Settings */}
        {activeTab === 'general' && (
          <div className="admin-settings__section">
            <h2>General Settings</h2>
            <div className="admin-settings__form">
              <div className="admin-settings__form-group">
                <label>Site Name</label>
                <input
                  type="text"
                  value={settings.siteName}
                  onChange={(e) => handleInputChange('siteName', e.target.value)}
                  className="admin-settings__input"
                />
              </div>
              <div className="admin-settings__form-group">
                <label>Site Description</label>
                <textarea
                  value={settings.siteDescription}
                  onChange={(e) => handleInputChange('siteDescription', e.target.value)}
                  className="admin-settings__textarea"
                  rows="3"
                />
              </div>
              <div className="admin-settings__form-group">
                <label>Contact Email</label>
                <input
                  type="email"
                  value={settings.contactEmail}
                  onChange={(e) => handleInputChange('contactEmail', e.target.value)}
                  className="admin-settings__input"
                />
              </div>
              <div className="admin-settings__form-group">
                <label>Support Email</label>
                <input
                  type="email"
                  value={settings.supportEmail}
                  onChange={(e) => handleInputChange('supportEmail', e.target.value)}
                  className="admin-settings__input"
                />
              </div>
              <div className="admin-settings__form-group">
                <label>Currency</label>
                <select
                  value={settings.currency}
                  onChange={(e) => handleInputChange('currency', e.target.value)}
                  className="admin-settings__select"
                >
                  <option value="USD">USD - US Dollar</option>
                  <option value="EUR">EUR - Euro</option>
                  <option value="GBP">GBP - British Pound</option>
                  <option value="CAD">CAD - Canadian Dollar</option>
                </select>
              </div>
              <div className="admin-settings__form-group">
                <label>Timezone</label>
                <select
                  value={settings.timezone}
                  onChange={(e) => handleInputChange('timezone', e.target.value)}
                  className="admin-settings__select"
                >
                  <option value="America/New_York">Eastern Time</option>
                  <option value="America/Chicago">Central Time</option>
                  <option value="America/Denver">Mountain Time</option>
                  <option value="America/Los_Angeles">Pacific Time</option>
                  <option value="UTC">UTC</option>
                </select>
              </div>
              <div className="admin-settings__form-group">
                <label className="admin-settings__checkbox-label">
                  <input
                    type="checkbox"
                    checked={settings.maintenanceMode}
                    onChange={(e) => handleInputChange('maintenanceMode', e.target.checked)}
                    className="admin-settings__checkbox"
                  />
                  Maintenance Mode
                </label>
                <p className="admin-settings__help-text">
                  Enable maintenance mode to temporarily disable the site for updates
                </p>
              </div>
            </div>
          </div>
        )}

        {/* User Settings */}
        {activeTab === 'users' && (
          <div className="admin-settings__section">
            <h2>User Settings</h2>
            <div className="admin-settings__form">
              <div className="admin-settings__form-group">
                <label className="admin-settings__checkbox-label">
                  <input
                    type="checkbox"
                    checked={settings.userRegistration}
                    onChange={(e) => handleInputChange('userRegistration', e.target.checked)}
                    className="admin-settings__checkbox"
                  />
                  Allow User Registration
                </label>
                <p className="admin-settings__help-text">
                  Allow new users to register on the platform
                </p>
              </div>
              <div className="admin-settings__form-group">
                <label className="admin-settings__checkbox-label">
                  <input
                    type="checkbox"
                    checked={settings.sellerApproval}
                    onChange={(e) => handleInputChange('sellerApproval', e.target.checked)}
                    className="admin-settings__checkbox"
                  />
                  Require Seller Approval
                </label>
                <p className="admin-settings__help-text">
                  Require admin approval before users can become sellers
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Content Settings */}
        {activeTab === 'content' && (
          <div className="admin-settings__section">
            <h2>Content Settings</h2>
            <div className="admin-settings__form">
              <div className="admin-settings__form-group">
                <label className="admin-settings__checkbox-label">
                  <input
                    type="checkbox"
                    checked={settings.contentModeration}
                    onChange={(e) => handleInputChange('contentModeration', e.target.checked)}
                    className="admin-settings__checkbox"
                  />
                  Content Moderation
                </label>
                <p className="admin-settings__help-text">
                  Require admin approval for all uploaded content
                </p>
              </div>
              <div className="admin-settings__form-group">
                <label>Maximum File Size (MB)</label>
                <input
                  type="number"
                  value={settings.maxFileSize}
                  onChange={(e) => handleInputChange('maxFileSize', parseInt(e.target.value))}
                  className="admin-settings__input"
                  min="1"
                  max="1000"
                />
              </div>
              <div className="admin-settings__form-group">
                <label>Allowed File Types</label>
                <div className="admin-settings__file-types">
                  {['pdf', 'doc', 'docx', 'mp4', 'mp3', 'jpg', 'png'].map(type => (
                    <label key={type} className="admin-settings__checkbox-label">
                      <input
                        type="checkbox"
                        checked={settings.allowedFileTypes.includes(type)}
                        onChange={(e) => {
                          const newTypes = e.target.checked
                            ? [...settings.allowedFileTypes, type]
                            : settings.allowedFileTypes.filter(t => t !== type);
                          handleInputChange('allowedFileTypes', newTypes);
                        }}
                        className="admin-settings__checkbox"
                      />
                      {type.toUpperCase()}
                    </label>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Payment Settings */}
        {activeTab === 'payments' && (
          <div className="admin-settings__section">
            <h2>Payment Settings</h2>
            <div className="admin-settings__form">
              <div className="admin-settings__form-group">
                <label>Commission Rate (%)</label>
                <input
                  type="number"
                  value={settings.commissionRate}
                  onChange={(e) => handleInputChange('commissionRate', parseInt(e.target.value))}
                  className="admin-settings__input"
                  min="0"
                  max="50"
                />
                <p className="admin-settings__help-text">
                  Platform commission rate for each sale
                </p>
              </div>
              <div className="admin-settings__form-group">
                <label>Minimum Withdrawal Amount</label>
                <input
                  type="number"
                  value={settings.minWithdrawal}
                  onChange={(e) => handleInputChange('minWithdrawal', parseInt(e.target.value))}
                  className="admin-settings__input"
                  min="1"
                />
                <p className="admin-settings__help-text">
                  Minimum amount sellers can withdraw
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Notification Settings */}
        {activeTab === 'notifications' && (
          <div className="admin-settings__section">
            <h2>Notification Settings</h2>
            <div className="admin-settings__form">
              <div className="admin-settings__form-group">
                <label className="admin-settings__checkbox-label">
                  <input
                    type="checkbox"
                    checked={settings.emailNotifications}
                    onChange={(e) => handleInputChange('emailNotifications', e.target.checked)}
                    className="admin-settings__checkbox"
                  />
                  Email Notifications
                </label>
                <p className="admin-settings__help-text">
                  Send email notifications to users
                </p>
              </div>
              <div className="admin-settings__form-group">
                <label className="admin-settings__checkbox-label">
                  <input
                    type="checkbox"
                    checked={settings.smsNotifications}
                    onChange={(e) => handleInputChange('smsNotifications', e.target.checked)}
                    className="admin-settings__checkbox"
                  />
                  SMS Notifications
                </label>
                <p className="admin-settings__help-text">
                  Send SMS notifications to users
                </p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Reset Confirmation */}
      <ConfirmDialog
        isOpen={showResetConfirm}
        onClose={() => setShowResetConfirm(false)}
        onConfirm={confirmReset}
        title="Reset Settings"
        message="Are you sure you want to reset all settings to their default values? This action cannot be undone."
        confirmText="Reset to Defaults"
        type="warning"
        loading={isProcessing}
      />
    </div>
  );
};

export default AdminSettings;
