import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useDebounce } from '../../hooks/useDebounce';
import Modal from '../../components/common/Modal';
import ConfirmDialog from '../../components/common/ConfirmDialog';
import { adminToasts } from '../../utils/toast';
import {
  fetchAllUsers,
  updateUserStatus,
  updateFilter,
  clearFilters,
  toggleSelectItem,
  selectAllItems,
  clearSelectedItems
} from '../../redux/slices/adminSlice';
import '../../styles/AdminUsers.css';

const AdminUsers = () => {
  const dispatch = useDispatch();
  const { users, loading, error, filters, selectedItems } = useSelector((state) => state.admin);
  const [currentPage, setCurrentPage] = useState(1);
  const [showFilters, setShowFilters] = useState(false);

  // Modal states
  const [selectedUser, setSelectedUser] = useState(null);
  const [showUserModal, setShowUserModal] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);
  const [userToDelete, setUserToDelete] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);

  // Debounce search to prevent excessive API calls
  const debouncedSearch = useDebounce(filters.users.search, 500);

  useEffect(() => {
    dispatch(fetchAllUsers({
      page: currentPage,
      ...filters.users,
      search: debouncedSearch
    }));
  }, [dispatch, currentPage, filters.users.role, filters.users.status, debouncedSearch]);

  const handleFilterChange = (filterName, value) => {
    dispatch(updateFilter({ section: 'users', filterName, value }));
    setCurrentPage(1);
  };

  const handleStatusUpdate = (userId, status) => {
    dispatch(updateUserStatus({ userId, status }));
  };

  const handleSelectUser = (userId) => {
    dispatch(toggleSelectItem({ section: 'users', itemId: userId }));
  };

  const handleSelectAll = () => {
    const allUserIds = users.data.map(user => user.id);
    if (selectedItems.users.length === allUserIds.length) {
      dispatch(clearSelectedItems({ section: 'users' }));
    } else {
      dispatch(selectAllItems({ section: 'users', itemIds: allUserIds }));
    }
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const getUserStatusBadge = (status) => {
    const statusClasses = {
      active: 'admin-users__status-badge--active',
      inactive: 'admin-users__status-badge--inactive',
      suspended: 'admin-users__status-badge--suspended',
      pending: 'admin-users__status-badge--pending'
    };

    return (
      <span className={`admin-users__status-badge ${statusClasses[status] || ''}`}>
        {status}
      </span>
    );
  };

  const getRoleBadge = (role) => {
    const roleClasses = {
      admin: 'admin-users__role-badge--admin',
      seller: 'admin-users__role-badge--seller',
      buyer: 'admin-users__role-badge--buyer'
    };

    return (
      <span className={`admin-users__role-badge ${roleClasses[role] || ''}`}>
        {role}
      </span>
    );
  };

  // Interactive functions
  const handleStatusUpdate = async (userId, newStatus) => {
    const user = users.data.find(u => u.id === userId);
    try {
      setIsProcessing(true);
      await dispatch(updateUserStatus({ userId, status: newStatus })).unwrap();
      adminToasts.userStatusChanged(user?.name || 'User', newStatus);
    } catch (error) {
      adminToasts.error('Failed to update user status');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleViewUser = (user) => {
    setSelectedUser(user);
    setShowUserModal(true);
  };

  const handleEditUser = (user) => {
    setSelectedUser(user);
    setShowUserModal(true);
    adminToasts.featureNotImplemented();
  };

  const handleDeleteUser = (user) => {
    setUserToDelete(user);
    setShowDeleteConfirm(true);
  };

  const confirmDeleteUser = async () => {
    if (!userToDelete) return;

    try {
      setIsProcessing(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      adminToasts.userDeleted(userToDelete.name);
      setShowDeleteConfirm(false);
      setUserToDelete(null);
    } catch (error) {
      adminToasts.error('Failed to delete user');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleBulkDelete = () => {
    if (selectedItems.users.length === 0) {
      adminToasts.validationError('selection');
      return;
    }
    setShowBulkDeleteConfirm(true);
  };

  const confirmBulkDelete = async () => {
    try {
      setIsProcessing(true);
      await new Promise(resolve => setTimeout(resolve, 1500));
      adminToasts.bulkActionCompleted(selectedItems.users.length, 'Delete');
      dispatch(clearSelectedItems({ section: 'users' }));
      setShowBulkDeleteConfirm(false);
    } catch (error) {
      adminToasts.error('Failed to delete users');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleBulkStatusChange = async (status) => {
    if (selectedItems.users.length === 0) {
      adminToasts.validationError('selection');
      return;
    }

    try {
      setIsProcessing(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      adminToasts.bulkActionCompleted(selectedItems.users.length, `Status changed to ${status}`);
      dispatch(clearSelectedItems({ section: 'users' }));
    } catch (error) {
      adminToasts.error('Failed to update user status');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleExportUsers = () => {
    adminToasts.exportStarted();
    setTimeout(() => {
      const csvContent = "data:text/csv;charset=utf-8," +
        "Name,Email,Role,Status,Joined\n" +
        users.data.map(user =>
          `${user.name},${user.email},${user.role},${user.status},${new Date(user.createdAt).toLocaleDateString()}`
        ).join("\n");

      const encodedUri = encodeURI(csvContent);
      const link = document.createElement("a");
      link.setAttribute("href", encodedUri);
      link.setAttribute("download", "users_export.csv");
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      adminToasts.success('Users exported successfully');
    }, 2000);
  };

  if (loading.users) {
    return (
      <div className="admin-users">
        <div className="admin-users__loading">
          <div className="admin-users__spinner"></div>
          <p>Loading users...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="admin-users">
      {/* Header */}
      <div className="admin-users__header">
        <div className="admin-users__title">
          <h1>User Management</h1>
          <p>Manage all users, their roles, and account status</p>
        </div>
        <div className="admin-users__actions">
          <button
            className="btn btn-outline"
            onClick={() => setShowFilters(!showFilters)}
          >
            Filters
          </button>
          <button
            className="btn btn-outline"
            onClick={handleExportUsers}
          >
            Export
          </button>
          <button
            className="btn btn-primary"
            onClick={() => adminToasts.featureNotImplemented()}
          >
            Add User
          </button>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="admin-users__filters">
          <div className="admin-users__filter-group">
            <input
              type="text"
              placeholder="Search users..."
              value={filters.users.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className="admin-users__search"
            />
            <select
              value={filters.users.role}
              onChange={(e) => handleFilterChange('role', e.target.value)}
              className="admin-users__select"
            >
              <option value="">All Roles</option>
              <option value="buyer">Buyer</option>
              <option value="seller">Seller</option>
              <option value="admin">Admin</option>
            </select>
            <select
              value={filters.users.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="admin-users__select"
            >
              <option value="">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="suspended">Suspended</option>
              <option value="pending">Pending</option>
            </select>
            <button
              className="btn btn-outline"
              onClick={() => dispatch(clearFilters({ section: 'users' }))}
            >
              Clear
            </button>
          </div>
        </div>
      )}

      {/* Stats */}
      <div className="admin-users__stats">
        <div className="admin-users__stat">
          <span className="admin-users__stat-value">{users.totalUsers}</span>
          <span className="admin-users__stat-label">Total Users</span>
        </div>
        <div className="admin-users__stat">
          <span className="admin-users__stat-value">{selectedItems.users.length}</span>
          <span className="admin-users__stat-label">Selected</span>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedItems.users.length > 0 && (
        <div className="admin-users__bulk-actions">
          <span>Selected {selectedItems.users.length} users</span>
          <div className="admin-users__bulk-buttons">
            <button
              className="btn btn-outline btn-sm"
              onClick={() => handleBulkStatusChange('active')}
              disabled={isProcessing}
            >
              Activate
            </button>
            <button
              className="btn btn-outline btn-sm"
              onClick={() => handleBulkStatusChange('suspended')}
              disabled={isProcessing}
            >
              Suspend
            </button>
            <button
              className="btn btn-outline btn-sm"
              onClick={handleBulkDelete}
              disabled={isProcessing}
            >
              Delete
            </button>
          </div>
        </div>
      )}

      {/* Users Table */}
      <div className="admin-users__table-container">
        <table className="admin-users__table">
          <thead>
            <tr>
              <th>
                <input
                  type="checkbox"
                  checked={selectedItems.users.length === users.data.length && users.data.length > 0}
                  onChange={handleSelectAll}
                />
              </th>
              <th>User</th>
              <th>Role</th>
              <th>Status</th>
              <th>Joined</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {users.data.map((user) => (
              <tr key={user.id} className="admin-users__table-row">
                <td>
                  <input
                    type="checkbox"
                    checked={selectedItems.users.includes(user.id)}
                    onChange={() => handleSelectUser(user.id)}
                  />
                </td>
                <td>
                  <div className="admin-users__user-info">
                    {user.avatar ? (
                      <img
                        src={user.avatar}
                        alt={user.name}
                        className="admin-users__avatar"
                        onError={(e) => {
                          e.target.style.display = 'none';
                          e.target.nextSibling.style.display = 'flex';
                        }}
                      />
                    ) : null}
                    <div
                      className="admin-users__avatar-fallback"
                      style={{ display: user.avatar ? 'none' : 'flex' }}
                    >
                      <span>👤</span>
                    </div>
                    <div>
                      <div className="admin-users__name">{user.name}</div>
                      <div className="admin-users__email">{user.email}</div>
                    </div>
                  </div>
                </td>
                <td>{getRoleBadge(user.role)}</td>
                <td>{getUserStatusBadge(user.status)}</td>
                <td>{new Date(user.createdAt).toLocaleDateString()}</td>
                <td>
                  <div className="admin-users__actions-cell">
                    <button
                      className="admin-users__action-btn"
                      onClick={() => handleViewUser(user)}
                    >
                      View
                    </button>
                    <button
                      className="admin-users__action-btn"
                      onClick={() => handleEditUser(user)}
                    >
                      Edit
                    </button>
                    <button
                      className="admin-users__action-btn admin-users__action-btn--danger"
                      onClick={() => handleDeleteUser(user)}
                    >
                      Delete
                    </button>
                    <select
                      value={user.status}
                      onChange={(e) => handleStatusUpdate(user.id, e.target.value)}
                      className="admin-users__status-select"
                      disabled={isProcessing}
                    >
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                      <option value="suspended">Suspended</option>
                    </select>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {users.totalPages > 1 && (
        <div className="admin-users__pagination">
          <button
            className="admin-users__page-btn"
            disabled={currentPage === 1}
            onClick={() => handlePageChange(currentPage - 1)}
          >
            Previous
          </button>

          {Array.from({ length: users.totalPages }, (_, i) => i + 1).map((page) => (
            <button
              key={page}
              className={`admin-users__page-btn ${currentPage === page ? 'admin-users__page-btn--active' : ''}`}
              onClick={() => handlePageChange(page)}
            >
              {page}
            </button>
          ))}

          <button
            className="admin-users__page-btn"
            disabled={currentPage === users.totalPages}
            onClick={() => handlePageChange(currentPage + 1)}
          >
            Next
          </button>
        </div>
      )}

      {error.users && (
        <div className="admin-users__error">
          <p>Error: {error.users}</p>
        </div>
      )}

      {/* User Details Modal */}
      <Modal
        isOpen={showUserModal}
        onClose={() => setShowUserModal(false)}
        title={selectedUser ? `User Details - ${selectedUser.name}` : 'User Details'}
        size="medium"
      >
        {selectedUser && (
          <div className="admin-users__user-details">
            <div className="admin-users__detail-row">
              <strong>Name:</strong> {selectedUser.name}
            </div>
            <div className="admin-users__detail-row">
              <strong>Email:</strong> {selectedUser.email}
            </div>
            <div className="admin-users__detail-row">
              <strong>Role:</strong> {selectedUser.role}
            </div>
            <div className="admin-users__detail-row">
              <strong>Status:</strong> {selectedUser.status}
            </div>
            <div className="admin-users__detail-row">
              <strong>Joined:</strong> {new Date(selectedUser.createdAt).toLocaleDateString()}
            </div>
            <div className="admin-users__detail-row">
              <strong>Last Login:</strong> {selectedUser.lastLogin ? new Date(selectedUser.lastLogin).toLocaleDateString() : 'Never'}
            </div>
            <div className="admin-users__detail-row">
              <strong>Total Orders:</strong> {selectedUser.totalOrders || 0}
            </div>
            <div className="admin-users__detail-row">
              <strong>Total Spent:</strong> ${selectedUser.totalSpent || 0}
            </div>
          </div>
        )}
      </Modal>

      {/* Delete User Confirmation */}
      <ConfirmDialog
        isOpen={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        onConfirm={confirmDeleteUser}
        title="Delete User"
        message={`Are you sure you want to delete user "${userToDelete?.name}"? This action cannot be undone.`}
        confirmText="Delete"
        type="danger"
        loading={isProcessing}
      />

      {/* Bulk Delete Confirmation */}
      <ConfirmDialog
        isOpen={showBulkDeleteConfirm}
        onClose={() => setShowBulkDeleteConfirm(false)}
        onConfirm={confirmBulkDelete}
        title="Delete Multiple Users"
        message={`Are you sure you want to delete ${selectedItems.users.length} selected users? This action cannot be undone.`}
        confirmText="Delete All"
        type="danger"
        loading={isProcessing}
      />
    </div>
  );
};

export default AdminUsers;
