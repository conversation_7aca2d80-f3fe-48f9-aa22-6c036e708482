/* User Edit Modal Styles */
.user-edit-modal {
  width: 100%;
  max-width: 600px;
}

.user-edit-modal__form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.user-edit-modal__section {
  border-bottom: 1px solid var(--light-gray);
  padding-bottom: var(--spacing-lg);
}

.user-edit-modal__section:last-of-type {
  border-bottom: none;
  padding-bottom: 0;
}

.user-edit-modal__section-title {
  font-size: var(--heading6);
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 2px solid var(--primary-color);
}

.user-edit-modal__form-group {
  margin-bottom: var(--spacing-md);
}

.user-edit-modal__form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-md);
}

.user-edit-modal__label {
  display: block;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: var(--spacing-xs);
  font-size: var(--smallfont);
}

.user-edit-modal__input,
.user-edit-modal__select,
.user-edit-modal__textarea {
  width: 100%;
  padding: var(--spacing-sm);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  font-family: inherit;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.user-edit-modal__input:focus,
.user-edit-modal__select:focus,
.user-edit-modal__textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(238, 52, 37, 0.1);
}

.user-edit-modal__input--error,
.user-edit-modal__select--error {
  border-color: var(--error-color);
}

.user-edit-modal__input--error:focus,
.user-edit-modal__select--error:focus {
  box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.1);
}

.user-edit-modal__textarea {
  resize: vertical;
  min-height: 80px;
}

.user-edit-modal__error {
  display: block;
  color: var(--error-color);
  font-size: var(--extrasmallfont);
  margin-top: var(--spacing-xs);
}

.user-edit-modal__actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--light-gray);
  margin-top: var(--spacing-lg);
}

.user-edit-modal__spinner {
  display: inline-block;
  width: 14px;
  height: 14px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: var(--spacing-xs);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Form Validation States */
.user-edit-modal__input:valid {
  border-color: var(--success-color);
}

.user-edit-modal__input:invalid {
  border-color: var(--error-color);
}

/* Button States */
.user-edit-modal__actions .btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.user-edit-modal__actions .btn-outline:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .user-edit-modal__form-row {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }

  .user-edit-modal__section {
    padding-bottom: var(--spacing-md);
  }

  .user-edit-modal__section-title {
    font-size: var(--basefont);
    margin-bottom: var(--spacing-sm);
  }

  .user-edit-modal__actions {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .user-edit-modal__actions .btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .user-edit-modal {
    padding: var(--spacing-sm);
  }

  .user-edit-modal__form {
    gap: var(--spacing-md);
  }

  .user-edit-modal__form-group {
    margin-bottom: var(--spacing-sm);
  }

  .user-edit-modal__input,
  .user-edit-modal__select,
  .user-edit-modal__textarea {
    padding: var(--spacing-xs);
    font-size: var(--smallfont);
  }

  .user-edit-modal__label {
    font-size: var(--extrasmallfont);
  }
}

/* Focus Management */
.user-edit-modal__input:focus-visible,
.user-edit-modal__select:focus-visible,
.user-edit-modal__textarea:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Accessibility */
.user-edit-modal__error {
  role: alert;
  aria-live: polite;
}

/* Loading State */
.user-edit-modal--loading {
  pointer-events: none;
  opacity: 0.7;
}

.user-edit-modal--loading .user-edit-modal__input,
.user-edit-modal--loading .user-edit-modal__select,
.user-edit-modal--loading .user-edit-modal__textarea {
  background-color: var(--bg-gray);
}
