import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { fetchDashboardStats } from '../../redux/slices/adminSlice';
import '../../styles/AdminAnalytics.css';

const AdminAnalytics = () => {
  const dispatch = useDispatch();
  const { dashboardStats, loading } = useSelector((state) => state.admin);
  const [selectedPeriod, setSelectedPeriod] = useState('30d');

  useEffect(() => {
    dispatch(fetchDashboardStats());
  }, [dispatch, selectedPeriod]);

  const analyticsData = {
    revenue: {
      current: 45678.90,
      previous: 38234.50,
      growth: 19.5
    },
    users: {
      current: 1247,
      previous: 1089,
      growth: 14.5
    },
    orders: {
      current: 1876,
      previous: 1654,
      growth: 13.4
    },
    conversion: {
      current: 3.2,
      previous: 2.8,
      growth: 14.3
    }
  };

  const chartData = [
    { month: 'Jan', revenue: 32000, users: 890, orders: 1200 },
    { month: 'Feb', revenue: 35000, users: 950, orders: 1350 },
    { month: 'Mar', revenue: 38000, users: 1020, orders: 1450 },
    { month: 'Apr', revenue: 42000, users: 1150, orders: 1600 },
    { month: 'May', revenue: 45678, users: 1247, orders: 1876 }
  ];

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatPercentage = (value) => {
    return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`;
  };

  if (loading.dashboard) {
    return (
      <div className="admin-analytics">
        <div className="admin-analytics__loading">
          <div className="admin-analytics__spinner"></div>
          <p>Loading analytics...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="admin-analytics">
      {/* Header */}
      <div className="admin-analytics__header">
        <div className="admin-analytics__title">
          <h1>Analytics Dashboard</h1>
          <p>Track performance metrics and business insights</p>
        </div>
        <div className="admin-analytics__controls">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="admin-analytics__period-select"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>
          <button className="btn btn-primary">Export Report</button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="admin-analytics__metrics">
        <div className="admin-analytics__metric-card">
          <div className="admin-analytics__metric-header">
            <h3>Total Revenue</h3>
            <span className="admin-analytics__metric-icon">💰</span>
          </div>
          <div className="admin-analytics__metric-value">
            {formatCurrency(analyticsData.revenue.current)}
          </div>
          <div className={`admin-analytics__metric-change ${analyticsData.revenue.growth > 0 ? 'positive' : 'negative'}`}>
            {formatPercentage(analyticsData.revenue.growth)} vs last period
          </div>
        </div>

        <div className="admin-analytics__metric-card">
          <div className="admin-analytics__metric-header">
            <h3>Total Users</h3>
            <span className="admin-analytics__metric-icon">👥</span>
          </div>
          <div className="admin-analytics__metric-value">
            {analyticsData.users.current.toLocaleString()}
          </div>
          <div className={`admin-analytics__metric-change ${analyticsData.users.growth > 0 ? 'positive' : 'negative'}`}>
            {formatPercentage(analyticsData.users.growth)} vs last period
          </div>
        </div>

        <div className="admin-analytics__metric-card">
          <div className="admin-analytics__metric-header">
            <h3>Total Orders</h3>
            <span className="admin-analytics__metric-icon">🛒</span>
          </div>
          <div className="admin-analytics__metric-value">
            {analyticsData.orders.current.toLocaleString()}
          </div>
          <div className={`admin-analytics__metric-change ${analyticsData.orders.growth > 0 ? 'positive' : 'negative'}`}>
            {formatPercentage(analyticsData.orders.growth)} vs last period
          </div>
        </div>

        <div className="admin-analytics__metric-card">
          <div className="admin-analytics__metric-header">
            <h3>Conversion Rate</h3>
            <span className="admin-analytics__metric-icon">📈</span>
          </div>
          <div className="admin-analytics__metric-value">
            {analyticsData.conversion.current}%
          </div>
          <div className={`admin-analytics__metric-change ${analyticsData.conversion.growth > 0 ? 'positive' : 'negative'}`}>
            {formatPercentage(analyticsData.conversion.growth)} vs last period
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="admin-analytics__charts">
        {/* Revenue Chart */}
        <div className="admin-analytics__chart-container">
          <div className="admin-analytics__chart-header">
            <h3>Revenue Trend</h3>
          </div>
          <div className="admin-analytics__chart">
            <div className="admin-analytics__chart-placeholder">
              <p>📊 Revenue Chart</p>
              <p>Interactive chart would be implemented here</p>
              <div className="admin-analytics__chart-data">
                {chartData.map((item, index) => (
                  <div key={index} className="admin-analytics__chart-bar">
                    <div className="admin-analytics__bar-label">{item.month}</div>
                    <div 
                      className="admin-analytics__bar"
                      style={{ height: `${(item.revenue / 50000) * 100}px` }}
                    ></div>
                    <div className="admin-analytics__bar-value">
                      {formatCurrency(item.revenue)}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* User Growth Chart */}
        <div className="admin-analytics__chart-container">
          <div className="admin-analytics__chart-header">
            <h3>User Growth</h3>
          </div>
          <div className="admin-analytics__chart">
            <div className="admin-analytics__chart-placeholder">
              <p>📈 User Growth Chart</p>
              <p>Line chart showing user acquisition over time</p>
              <div className="admin-analytics__growth-stats">
                <div className="admin-analytics__growth-item">
                  <span className="admin-analytics__growth-label">New Users</span>
                  <span className="admin-analytics__growth-value">+158</span>
                </div>
                <div className="admin-analytics__growth-item">
                  <span className="admin-analytics__growth-label">Active Users</span>
                  <span className="admin-analytics__growth-value">1,089</span>
                </div>
                <div className="admin-analytics__growth-item">
                  <span className="admin-analytics__growth-label">Retention Rate</span>
                  <span className="admin-analytics__growth-value">87.3%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Performance Summary */}
      <div className="admin-analytics__summary">
        <div className="admin-analytics__summary-header">
          <h3>Performance Summary</h3>
        </div>
        <div className="admin-analytics__summary-grid">
          <div className="admin-analytics__summary-item">
            <h4>Top Performing Content</h4>
            <ul>
              <li>Basketball Training Fundamentals - 78 downloads</li>
              <li>Soccer Coaching Techniques - 65 downloads</li>
              <li>Football Strategy Playbook - 45 downloads</li>
            </ul>
          </div>
          <div className="admin-analytics__summary-item">
            <h4>User Activity</h4>
            <ul>
              <li>Average session duration: 12m 34s</li>
              <li>Pages per session: 4.2</li>
              <li>Bounce rate: 23.5%</li>
            </ul>
          </div>
          <div className="admin-analytics__summary-item">
            <h4>Revenue Breakdown</h4>
            <ul>
              <li>Content Sales: {formatCurrency(35000)}</li>
              <li>Subscriptions: {formatCurrency(8000)}</li>
              <li>Commission: {formatCurrency(2678.90)}</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminAnalytics;
