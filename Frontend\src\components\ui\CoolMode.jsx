import React, { useRef, useCallback } from 'react';
import './CoolMode.css';

const CoolMode = ({ 
  children, 
  options = {},
  disabled = false 
}) => {
  const containerRef = useRef(null);
  
  const defaultOptions = {
    particle: "🎉", // Default particle
    particleCount: 6,
    speedHorz: { min: -10, max: 10 },
    speedUp: { min: -20, max: -10 },
    gravity: 0.35,
    life: { min: 0.8, max: 1.2 },
    colors: ["#ff0000", "#00ff00", "#0000ff", "#ffff00", "#ff00ff", "#00ffff"],
    shapes: ["square", "circle"],
    size: { min: 8, max: 12 },
    ...options
  };

  const createParticle = useCallback((x, y) => {
    if (!containerRef.current || disabled) return;

    const particle = document.createElement('div');
    particle.className = 'cool-mode-particle';
    
    // Set particle content
    if (defaultOptions.particle.startsWith('http')) {
      // If it's an image URL
      particle.innerHTML = `<img src="${defaultOptions.particle}" alt="particle" style="width: 100%; height: 100%; object-fit: cover; border-radius: 50%;" />`;
    } else {
      // If it's an emoji or text
      particle.textContent = defaultOptions.particle;
    }
    
    // Random properties
    const speedX = Math.random() * (defaultOptions.speedHorz.max - defaultOptions.speedHorz.min) + defaultOptions.speedHorz.min;
    const speedY = Math.random() * (defaultOptions.speedUp.max - defaultOptions.speedUp.min) + defaultOptions.speedUp.min;
    const life = Math.random() * (defaultOptions.life.max - defaultOptions.life.min) + defaultOptions.life.min;
    const size = Math.random() * (defaultOptions.size.max - defaultOptions.size.min) + defaultOptions.size.min;
    const color = defaultOptions.colors[Math.floor(Math.random() * defaultOptions.colors.length)];
    const shape = defaultOptions.shapes[Math.floor(Math.random() * defaultOptions.shapes.length)];
    
    // Set initial styles
    particle.style.cssText = `
      position: fixed;
      left: ${x}px;
      top: ${y}px;
      width: ${size}px;
      height: ${size}px;
      background-color: ${defaultOptions.particle.startsWith('http') ? 'transparent' : color};
      border-radius: ${shape === 'circle' ? '50%' : '0'};
      pointer-events: none;
      z-index: 9999;
      font-size: ${size}px;
      line-height: 1;
      user-select: none;
      transform-origin: center;
    `;
    
    document.body.appendChild(particle);
    
    // Animation variables
    let currentX = x;
    let currentY = y;
    let velocityX = speedX;
    let velocityY = speedY;
    let opacity = 1;
    let scale = 1;
    let rotation = 0;
    let startTime = Date.now();
    
    const animate = () => {
      const elapsed = (Date.now() - startTime) / 1000;
      
      if (elapsed >= life) {
        particle.remove();
        return;
      }
      
      // Update physics
      velocityY += defaultOptions.gravity;
      currentX += velocityX;
      currentY += velocityY;
      
      // Update visual properties
      const progress = elapsed / life;
      opacity = 1 - progress;
      scale = 1 - progress * 0.5;
      rotation += 5;
      
      // Apply transforms
      particle.style.transform = `translate(${currentX - x}px, ${currentY - y}px) scale(${scale}) rotate(${rotation}deg)`;
      particle.style.opacity = opacity;
      
      requestAnimationFrame(animate);
    };
    
    requestAnimationFrame(animate);
  }, [defaultOptions, disabled]);

  const handleClick = useCallback((event) => {
    if (disabled) return;
    
    const rect = event.currentTarget.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    // Create multiple particles
    for (let i = 0; i < defaultOptions.particleCount; i++) {
      setTimeout(() => {
        createParticle(centerX, centerY);
      }, i * 50); // Stagger particle creation
    }
  }, [createParticle, defaultOptions.particleCount, disabled]);

  return (
    <div 
      ref={containerRef}
      className="cool-mode-container"
      onClick={handleClick}
      style={{ display: 'inline-block', cursor: disabled ? 'default' : 'pointer' }}
    >
      {children}
    </div>
  );
};

export default CoolMode;
