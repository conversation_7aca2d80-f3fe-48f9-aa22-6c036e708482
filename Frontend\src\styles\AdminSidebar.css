/* Admin Sidebar Styles */
.admin-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  width: 280px;
  height: 100vh;
  background-color: var(--white);
  border-right: 1px solid var(--light-gray);
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease, transform 0.3s ease;
  z-index: var(--z-index-header);
  box-shadow: var(--box-shadow-light);
}

.admin-sidebar--collapsed {
  width: 80px;
}

.admin-sidebar__header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--light-gray);
}

.admin-sidebar__logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.admin-sidebar__logo-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--primary-color), var(--btn-color));
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-weight: 700;
  font-size: var(--heading6);
}

.admin-sidebar__logo-text h3 {
  color: var(--text-color);
  font-size: var(--heading6);
  font-weight: 600;
  margin: 0;
}

.admin-sidebar__user {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--light-gray);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.admin-sidebar__user-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
  position: relative;
  background-color: var(--bg-gray);
}

.admin-sidebar__user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.admin-sidebar__user-avatar-fallback {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary-light-color);
  color: var(--primary-color);
  font-size: 24px;
}

.admin-sidebar__user-info h4 {
  color: var(--text-color);
  font-size: var(--basefont);
  font-weight: 600;
  margin: 0 0 4px 0;
}

.admin-sidebar__user-info p {
  color: var(--dark-gray);
  font-size: var(--smallfont);
  margin: 0;
}

.admin-sidebar__nav {
  flex: 1;
  padding: var(--spacing-md) 0;
  overflow-y: auto;
}

.admin-sidebar__menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

.admin-sidebar__menu-item {
  margin-bottom: 4px;
}

.admin-sidebar__menu-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  color: var(--dark-gray);
  text-decoration: none;
  transition: all 0.3s ease;
  border-radius: 0 25px 25px 0;
  margin-right: var(--spacing-md);
}

.admin-sidebar__menu-link:hover {
  background-color: var(--primary-light-color);
  color: var(--primary-color);
}

.admin-sidebar__menu-link--active {
  background-color: var(--primary-color);
  color: var(--white);
}

.admin-sidebar__menu-link--active:hover {
  background-color: var(--primary-color);
  color: var(--white);
}

.admin-sidebar__menu-icon {
  font-size: var(--heading5);
  width: 24px;
  text-align: center;
  flex-shrink: 0;
}

.admin-sidebar__menu-label {
  font-size: var(--basefont);
  font-weight: 500;
}

.admin-sidebar__footer {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--light-gray);
}

.admin-sidebar__version {
  color: var(--dark-gray);
  font-size: var(--smallfont);
  text-align: center;
  margin: 0;
}

/* Collapsed State */
.admin-sidebar--collapsed .admin-sidebar__logo-text,
.admin-sidebar--collapsed .admin-sidebar__user-info,
.admin-sidebar--collapsed .admin-sidebar__menu-label,
.admin-sidebar--collapsed .admin-sidebar__footer-content {
  display: none;
}

.admin-sidebar--collapsed .admin-sidebar__user {
  justify-content: center;
}

.admin-sidebar--collapsed .admin-sidebar__menu-link {
  justify-content: center;
  margin-right: 0;
  border-radius: var(--border-radius);
  margin: 4px var(--spacing-sm);
}

/* Tablet Responsive */
@media (max-width: 1024px) {
  .admin-sidebar {
    width: 240px;
  }

  .admin-sidebar--collapsed {
    width: 70px;
  }

  .admin-sidebar__nav-item {
    padding: var(--spacing-md) var(--spacing-sm);
  }

  .admin-sidebar__nav-text {
    font-size: var(--smallfont);
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .admin-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: 280px;
    z-index: var(--z-index-sidebar);
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    box-shadow: var(--box-shadow);
    background-color: var(--white);
  }

  .admin-sidebar--open {
    transform: translateX(0);
  }

  .admin-sidebar--collapsed {
    width: 280px;
    transform: translateX(-100%);
  }

  .admin-sidebar--collapsed.admin-sidebar--open {
    transform: translateX(0);
  }

  .admin-sidebar__nav-item {
    padding: var(--spacing-md);
  }

  .admin-sidebar__nav-text {
    display: block;
  }

  .admin-sidebar__user-info {
    display: block;
  }

  .admin-sidebar__user {
    padding: var(--spacing-lg) var(--spacing-md);
  }

  .admin-sidebar__user-avatar,
  .admin-sidebar__user-avatar-fallback {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }
}

/* Small Mobile */
@media (max-width: 480px) {
  .admin-sidebar {
    width: 100vw;
    max-width: 320px;
  }

  .admin-sidebar__header {
    padding: var(--spacing-md);
  }

  .admin-sidebar__logo {
    font-size: var(--heading4);
  }

  .admin-sidebar__nav-item {
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .admin-sidebar__nav-icon {
    font-size: 18px;
  }

  .admin-sidebar__nav-text {
    font-size: var(--basefont);
  }

  .admin-sidebar__user {
    padding: var(--spacing-md);
  }

  .admin-sidebar__user-info h4 {
    font-size: var(--basefont);
  }

  .admin-sidebar__user-info p {
    font-size: var(--smallfont);
  }
}

/* Extra small screens */
@media (max-width: 360px) {
  .admin-sidebar {
    width: 100vw;
  }

  .admin-sidebar__nav-item {
    padding: var(--spacing-xs) var(--spacing-md);
  }

  .admin-sidebar__user {
    padding: var(--spacing-sm) var(--spacing-md);
  }
}
