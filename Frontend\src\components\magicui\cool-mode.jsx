"use client";

import React, { useEffect, useRef } from "react";

export const CoolMode = ({ children, options }) => {
  const ref = useRef(null);

  useEffect(() => {
    if (ref.current) {
      return applyParticleEffect(ref.current, options);
    }
  }, [options]);

  return React.cloneElement(children, { ref });
};

const applyParticleEffect = (element, options = {}) => {
  const {
    particle = "sports",
  } = options;

  let particles = [];
  let autoAddParticle = false;
  let mouseX = 0;
  let mouseY = 0;

  const container = getContainer();

  function generateParticle() {
    const sizes = [30, 35, 40, 45, 50]; // Good sizes for sports tools
    const size = sizes[Math.floor(Math.random() * sizes.length)];
    const speedHorz = Math.random() * 10;
    const speedUp = Math.random() * 25;
    const spinVal = Math.random() * 360;
    const spinSpeed = Math.random() * 35 * (Math.random() <= 0.5 ? -1 : 1);
    const top = mouseY - size / 2;
    const left = mouseX - size / 2;
    const direction = Math.random() <= 0.5 ? -1 : 1;

    const particleElement = document.createElement("div");

    // Sports tools array for random selection
    const sportsTools = [
      "⚽", "🏀", "🏈", "⚾", "🎾", "🏐", "🏓", "🏸", "🏒", "🏑",
      "🥍", "🏏", "🥎", "🏆", "🥇", "🥈", "🥉", "🎯", "🏹", "🎱"
    ];

    if (particle === "circle") {
      const svgNS = "http://www.w3.org/2000/svg";
      const circleSVG = document.createElementNS(svgNS, "svg");
      const circle = document.createElementNS(svgNS, "circle");
      circle.setAttributeNS(null, "cx", (size / 2).toString());
      circle.setAttributeNS(null, "cy", (size / 2).toString());
      circle.setAttributeNS(null, "r", (size / 2).toString());
      circle.setAttributeNS(
        null,
        "fill",
        `hsl(${Math.random() * 360}, 70%, 50%)`,
      );

      circleSVG.appendChild(circle);
      circleSVG.setAttribute("width", size.toString());
      circleSVG.setAttribute("height", size.toString());

      particleElement.appendChild(circleSVG);
    } else if (particle === "sports") {
      // Use random sports tool
      const selectedTool = sportsTools[Math.floor(Math.random() * sportsTools.length)];
      particleElement.innerHTML = selectedTool;
    } else if (particle.startsWith("http")) {
      // Image URL
      particleElement.innerHTML = `<img src="${particle}" width="${size}" height="${size}" style="border-radius: 50%">`;
    } else {
      // Single emoji or text
      particleElement.innerHTML = particle;
    }

    particleElement.setAttribute(
      "style",
      [
        "position:absolute",
        "will-change:transform",
        `top:${top}px`,
        `left:${left}px`,
        `transform:rotate(${spinVal}deg)`,
        `font-size:${size}px`,
        "line-height:1",
        "text-align:center",
        "display:flex",
        "align-items:center",
        "justify-content:center",
      ].join(";"),
    );

    container.appendChild(particleElement);

    particles.push({
      element: particleElement,
      left,
      top,
      speedHorz,
      speedUp,
      direction,
      spinVal,
      spinSpeed,
      size,
    });
  }

  function refreshParticles() {
    particles.forEach((p) => {
      p.left = p.left - p.speedHorz * p.direction;
      p.top = p.top - p.speedUp;
      p.speedUp = Math.min(p.size, p.speedUp - 1);
      p.spinVal = p.spinVal + p.spinSpeed;

      if (
        p.top >=
        Math.max(window.innerHeight, document.body.clientHeight) + p.size
      ) {
        particles = particles.filter((o) => o !== p);
        p.element.remove();
      }

      p.element.setAttribute(
        "style",
        [
          "position:absolute",
          "will-change:transform",
          `top:${p.top}px`,
          `left:${p.left}px`,
          `transform:rotate(${p.spinVal}deg)`,
          `font-size:${p.size}px`,
          "line-height:1",
          "text-align:center",
          "display:flex",
          "align-items:center",
          "justify-content:center",
        ].join(";"),
      );
    });
  }

  function handlePointerMove(e) {
    const rect = element.getBoundingClientRect();
    mouseX = e.clientX - rect.left;
    mouseY = e.clientY - rect.top;
  }

  function handlePointerDown() {
    autoAddParticle = true;
    generateParticle();
  }

  function handlePointerUp() {
    autoAddParticle = false;
  }

  element.addEventListener("pointerdown", handlePointerDown);
  element.addEventListener("pointerup", handlePointerUp);
  element.addEventListener("pointermove", handlePointerMove);

  const interval = setInterval(() => {
    if (autoAddParticle) {
      generateParticle();
    }
    refreshParticles();
  }, 16);

  return () => {
    clearInterval(interval);
    element.removeEventListener("pointerdown", handlePointerDown);
    element.removeEventListener("pointerup", handlePointerUp);
    element.removeEventListener("pointermove", handlePointerMove);
  };
};

const getContainer = () => {
  const id = "_coolMode_effect";
  let existingContainer = document.getElementById(id);

  if (existingContainer) {
    return existingContainer;
  }

  const container = document.createElement("div");
  container.setAttribute("id", id);
  container.setAttribute(
    "style",
    "overflow:hidden; position:fixed; height:100%; top:0; left:0; right:0; bottom:0; pointer-events:none; z-index:2147483647",
  );

  document.body.appendChild(container);

  return container;
};
