import api from './api';
import {
  mockDashboardStats,
  mockUsers,
  mockContent,
  mockOrders,
  mockApiDelay,
  mockApiResponse,
  mockApiError
} from '../data/mockAdminData';

// Use mock data in development
const USE_MOCK_DATA = process.env.NODE_ENV === 'development';
// Enable/disable error simulation (set to false to disable errors completely)
const ENABLE_ERROR_SIMULATION = false; // Temporarily disabled for stable testing

// Request cache to prevent duplicate requests
const requestCache = new Map();
const CACHE_DURATION = 5000; // 5 seconds

const getCacheKey = (url, params) => {
  return `${url}?${JSON.stringify(params)}`;
};

const getCachedRequest = (key) => {
  const cached = requestCache.get(key);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.data;
  }
  return null;
};

const setCachedRequest = (key, data) => {
  requestCache.set(key, {
    data,
    timestamp: Date.now()
  });
};

const adminService = {
  // Dashboard Statistics
  getDashboardStats: async () => {
    const cacheKey = getCacheKey('/admin/dashboard/stats', {});
    const cached = getCachedRequest(cacheKey);

    if (cached) {
      return cached;
    }

    if (USE_MOCK_DATA) {
      await mockApiDelay(500);
      const response = mockApiResponse(mockDashboardStats);
      setCachedRequest(cacheKey, response);
      return response;
    }

    try {
      const response = await api.get('/admin/dashboard/stats');
      setCachedRequest(cacheKey, response);
      return response;
    } catch (error) {
      throw error;
    }
  },

  // User Management
  getAllUsers: async ({ page = 1, limit = 10, search = '', role = '', status = '' }) => {
    const params = { page, limit, search, role, status };
    const cacheKey = getCacheKey('/admin/users', params);
    const cached = getCachedRequest(cacheKey);

    if (cached) {
      return cached;
    }

    if (USE_MOCK_DATA) {
      await mockApiDelay(800);
      if (ENABLE_ERROR_SIMULATION) {
        mockApiError(); // Simulate potential errors
      }

      // Filter mock data based on parameters
      let filteredUsers = [...mockUsers.data];

      if (search) {
        filteredUsers = filteredUsers.filter(user =>
          user.name.toLowerCase().includes(search.toLowerCase()) ||
          user.email.toLowerCase().includes(search.toLowerCase())
        );
      }

      if (role) {
        filteredUsers = filteredUsers.filter(user => user.role === role);
      }

      if (status) {
        filteredUsers = filteredUsers.filter(user => user.status === status);
      }

      const response = mockApiResponse({
        ...mockUsers,
        data: filteredUsers
      });
      setCachedRequest(cacheKey, response);
      return response;
    }

    try {
      const urlParams = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...(search && { search }),
        ...(role && { role }),
        ...(status && { status })
      });

      const response = await api.get(`/admin/users?${urlParams}`);
      setCachedRequest(cacheKey, response);
      return response;
    } catch (error) {
      throw error;
    }
  },

  getUserById: async (userId) => {
    try {
      const response = await api.get(`/admin/users/${userId}`);
      return response;
    } catch (error) {
      throw error;
    }
  },

  updateUserStatus: async (userId, status) => {
    if (USE_MOCK_DATA) {
      await mockApiDelay(300);
      // Find and update user in mock data
      const user = mockUsers.data.find(u => u.id === parseInt(userId));
      if (user) {
        user.status = status;
        return mockApiResponse(user);
      }
      throw new Error('User not found');
    }

    try {
      const response = await api.patch(`/admin/users/${userId}/status`, { status });
      return response;
    } catch (error) {
      throw error;
    }
  },

  deleteUser: async (userId) => {
    try {
      const response = await api.delete(`/admin/users/${userId}`);
      return response;
    } catch (error) {
      throw error;
    }
  },

  bulkUpdateUsers: async (userIds, action, data = {}) => {
    try {
      const response = await api.patch('/admin/users/bulk', {
        userIds,
        action,
        data
      });
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Content Management
  getAllContent: async ({ page = 1, limit = 10, search = '', status = '', category = '' }) => {
    if (USE_MOCK_DATA) {
      await mockApiDelay(800);
      if (ENABLE_ERROR_SIMULATION) {
        mockApiError(); // Simulate potential errors
      }

      // Filter mock data based on parameters
      let filteredContent = [...mockContent.data];

      if (search) {
        filteredContent = filteredContent.filter(content =>
          content.title.toLowerCase().includes(search.toLowerCase()) ||
          content.description.toLowerCase().includes(search.toLowerCase())
        );
      }

      if (status) {
        filteredContent = filteredContent.filter(content => content.status === status);
      }

      if (category) {
        filteredContent = filteredContent.filter(content => content.category === category);
      }

      return mockApiResponse({
        ...mockContent,
        data: filteredContent
      });
    }

    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...(search && { search }),
        ...(status && { status }),
        ...(category && { category })
      });

      const response = await api.get(`/admin/content?${params}`);
      return response;
    } catch (error) {
      throw error;
    }
  },

  getContentById: async (contentId) => {
    try {
      const response = await api.get(`/admin/content/${contentId}`);
      return response;
    } catch (error) {
      throw error;
    }
  },

  updateContentStatus: async (contentId, status, reason = '') => {
    if (USE_MOCK_DATA) {
      await mockApiDelay(300);
      // Find and update content in mock data
      const content = mockContent.data.find(c => c.id === parseInt(contentId));
      if (content) {
        content.status = status;
        return mockApiResponse(content);
      }
      throw new Error('Content not found');
    }

    try {
      const response = await api.patch(`/admin/content/${contentId}/status`, {
        status,
        reason
      });
      return response;
    } catch (error) {
      throw error;
    }
  },

  deleteContent: async (contentId) => {
    try {
      const response = await api.delete(`/admin/content/${contentId}`);
      return response;
    } catch (error) {
      throw error;
    }
  },

  bulkUpdateContent: async (contentIds, action, data = {}) => {
    try {
      const response = await api.patch('/admin/content/bulk', {
        contentIds,
        action,
        data
      });
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Order Management
  getAllOrders: async ({ page = 1, limit = 10, search = '', status = '', dateRange = '' }) => {
    if (USE_MOCK_DATA) {
      await mockApiDelay(800);
      // Filter mock data based on parameters
      let filteredOrders = [...mockOrders.data];

      if (search) {
        filteredOrders = filteredOrders.filter(order =>
          order.orderNumber.toLowerCase().includes(search.toLowerCase()) ||
          order.buyer.name.toLowerCase().includes(search.toLowerCase()) ||
          order.buyer.email.toLowerCase().includes(search.toLowerCase()) ||
          order.content.title.toLowerCase().includes(search.toLowerCase())
        );
      }

      if (status) {
        filteredOrders = filteredOrders.filter(order => order.status === status);
      }

      // Simple date range filtering (you can enhance this)
      if (dateRange) {
        const now = new Date();
        const filterDate = new Date();

        switch (dateRange) {
          case 'today':
            filterDate.setHours(0, 0, 0, 0);
            break;
          case 'week':
            filterDate.setDate(now.getDate() - 7);
            break;
          case 'month':
            filterDate.setMonth(now.getMonth() - 1);
            break;
          case 'quarter':
            filterDate.setMonth(now.getMonth() - 3);
            break;
          default:
            filterDate.setFullYear(1970);
        }

        filteredOrders = filteredOrders.filter(order =>
          new Date(order.createdAt) >= filterDate
        );
      }

      return mockApiResponse({
        ...mockOrders,
        data: filteredOrders
      });
    }

    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...(search && { search }),
        ...(status && { status }),
        ...(dateRange && { dateRange })
      });

      const response = await api.get(`/admin/orders?${params}`);
      return response;
    } catch (error) {
      throw error;
    }
  },

  getOrderById: async (orderId) => {
    try {
      const response = await api.get(`/admin/orders/${orderId}`);
      return response;
    } catch (error) {
      throw error;
    }
  },

  updateOrderStatus: async (orderId, status) => {
    try {
      const response = await api.patch(`/admin/orders/${orderId}/status`, { status });
      return response;
    } catch (error) {
      throw error;
    }
  },

  refundOrder: async (orderId, amount, reason) => {
    try {
      const response = await api.post(`/admin/orders/${orderId}/refund`, {
        amount,
        reason
      });
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Analytics
  getAnalytics: async (period = '30d') => {
    try {
      const response = await api.get(`/admin/analytics?period=${period}`);
      return response;
    } catch (error) {
      throw error;
    }
  },

  getRevenueAnalytics: async (period = '30d') => {
    try {
      const response = await api.get(`/admin/analytics/revenue?period=${period}`);
      return response;
    } catch (error) {
      throw error;
    }
  },

  getUserAnalytics: async (period = '30d') => {
    try {
      const response = await api.get(`/admin/analytics/users?period=${period}`);
      return response;
    } catch (error) {
      throw error;
    }
  },

  getContentAnalytics: async (period = '30d') => {
    try {
      const response = await api.get(`/admin/analytics/content?period=${period}`);
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Reports
  generateReport: async (type, filters = {}) => {
    try {
      const response = await api.post('/admin/reports/generate', {
        type,
        filters
      });
      return response;
    } catch (error) {
      throw error;
    }
  },

  downloadReport: async (reportId) => {
    try {
      const response = await api.get(`/admin/reports/${reportId}/download`, {
        responseType: 'blob'
      });
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Settings
  getSystemSettings: async () => {
    try {
      const response = await api.get('/admin/settings');
      return response;
    } catch (error) {
      throw error;
    }
  },

  updateSystemSettings: async (settings) => {
    try {
      const response = await api.patch('/admin/settings', settings);
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Notifications
  sendNotification: async (notification) => {
    try {
      const response = await api.post('/admin/notifications/send', notification);
      return response;
    } catch (error) {
      throw error;
    }
  },

  getNotificationTemplates: async () => {
    try {
      const response = await api.get('/admin/notifications/templates');
      return response;
    } catch (error) {
      throw error;
    }
  },

  // Activity Logs
  getActivityLogs: async ({ page = 1, limit = 10, userId = '', action = '' }) => {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...(userId && { userId }),
        ...(action && { action })
      });

      const response = await api.get(`/admin/activity-logs?${params}`);
      return response;
    } catch (error) {
      throw error;
    }
  }
};

export default adminService;
