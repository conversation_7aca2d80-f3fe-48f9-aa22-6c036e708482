import React, { useState, useEffect } from 'react';
import Modal from '../common/Modal';
import { adminToasts } from '../../utils/toast';
import '../../styles/UserEditModal.css';

const UserEditModal = ({ 
  isOpen, 
  onClose, 
  user, 
  onSave,
  loading = false 
}) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    role: 'buyer',
    status: 'active',
    phone: '',
    address: '',
    bio: ''
  });
  
  const [errors, setErrors] = useState({});
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    if (user) {
      setFormData({
        name: user.name || '',
        email: user.email || '',
        role: user.role || 'buyer',
        status: user.status || 'active',
        phone: user.phone || '',
        address: user.address || '',
        bio: user.bio || ''
      });
      setHasChanges(false);
      setErrors({});
    }
  }, [user]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    setHasChanges(true);
    
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }

    if (!formData.role) {
      newErrors.role = 'Role is required';
    }

    if (!formData.status) {
      newErrors.status = 'Status is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = () => {
    if (!validateForm()) {
      adminToasts.validationError('form fields');
      return;
    }

    onSave({
      ...user,
      ...formData
    });
  };

  const handleClose = () => {
    if (hasChanges) {
      if (window.confirm('You have unsaved changes. Are you sure you want to close?')) {
        onClose();
      }
    } else {
      onClose();
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={user ? `Edit User - ${user.name}` : 'Edit User'}
      size="medium"
    >
      <div className="user-edit-modal">
        <form className="user-edit-modal__form" onSubmit={(e) => e.preventDefault()}>
          {/* Basic Information */}
          <div className="user-edit-modal__section">
            <h3 className="user-edit-modal__section-title">Basic Information</h3>
            
            <div className="user-edit-modal__form-group">
              <label className="user-edit-modal__label">
                Name *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className={`user-edit-modal__input ${errors.name ? 'user-edit-modal__input--error' : ''}`}
                placeholder="Enter full name"
              />
              {errors.name && (
                <span className="user-edit-modal__error">{errors.name}</span>
              )}
            </div>

            <div className="user-edit-modal__form-group">
              <label className="user-edit-modal__label">
                Email *
              </label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className={`user-edit-modal__input ${errors.email ? 'user-edit-modal__input--error' : ''}`}
                placeholder="Enter email address"
              />
              {errors.email && (
                <span className="user-edit-modal__error">{errors.email}</span>
              )}
            </div>

            <div className="user-edit-modal__form-row">
              <div className="user-edit-modal__form-group">
                <label className="user-edit-modal__label">
                  Role *
                </label>
                <select
                  value={formData.role}
                  onChange={(e) => handleInputChange('role', e.target.value)}
                  className={`user-edit-modal__select ${errors.role ? 'user-edit-modal__select--error' : ''}`}
                >
                  <option value="buyer">Buyer</option>
                  <option value="seller">Seller</option>
                  <option value="admin">Admin</option>
                </select>
                {errors.role && (
                  <span className="user-edit-modal__error">{errors.role}</span>
                )}
              </div>

              <div className="user-edit-modal__form-group">
                <label className="user-edit-modal__label">
                  Status *
                </label>
                <select
                  value={formData.status}
                  onChange={(e) => handleInputChange('status', e.target.value)}
                  className={`user-edit-modal__select ${errors.status ? 'user-edit-modal__select--error' : ''}`}
                >
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                  <option value="suspended">Suspended</option>
                </select>
                {errors.status && (
                  <span className="user-edit-modal__error">{errors.status}</span>
                )}
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="user-edit-modal__section">
            <h3 className="user-edit-modal__section-title">Contact Information</h3>
            
            <div className="user-edit-modal__form-group">
              <label className="user-edit-modal__label">
                Phone Number
              </label>
              <input
                type="tel"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                className="user-edit-modal__input"
                placeholder="Enter phone number"
              />
            </div>

            <div className="user-edit-modal__form-group">
              <label className="user-edit-modal__label">
                Address
              </label>
              <textarea
                value={formData.address}
                onChange={(e) => handleInputChange('address', e.target.value)}
                className="user-edit-modal__textarea"
                placeholder="Enter address"
                rows="3"
              />
            </div>
          </div>

          {/* Additional Information */}
          <div className="user-edit-modal__section">
            <h3 className="user-edit-modal__section-title">Additional Information</h3>
            
            <div className="user-edit-modal__form-group">
              <label className="user-edit-modal__label">
                Bio
              </label>
              <textarea
                value={formData.bio}
                onChange={(e) => handleInputChange('bio', e.target.value)}
                className="user-edit-modal__textarea"
                placeholder="Enter user bio"
                rows="4"
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="user-edit-modal__actions">
            <button
              type="button"
              className="btn btn-outline"
              onClick={handleClose}
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="button"
              className={`btn ${hasChanges ? 'btn-primary' : 'btn-outline'}`}
              onClick={handleSave}
              disabled={loading || !hasChanges}
            >
              {loading ? (
                <>
                  <span className="user-edit-modal__spinner"></span>
                  Saving...
                </>
              ) : (
                'Save Changes'
              )}
            </button>
          </div>
        </form>
      </div>
    </Modal>
  );
};

export default UserEditModal;
