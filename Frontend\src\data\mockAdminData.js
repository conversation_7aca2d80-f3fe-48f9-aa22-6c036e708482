// Mock data for admin panel development and testing

export const mockDashboardStats = {
  totalUsers: 1247,
  totalSellers: 89,
  totalBuyers: 1158,
  totalContent: 342,
  totalOrders: 1876,
  totalRevenue: 45678.90,
  pendingApprovals: 12,
  recentActivity: [
    {
      id: 1,
      type: 'user_registration',
      message: 'New user <PERSON> registered',
      time: '2 minutes ago',
      icon: '👤'
    },
    {
      id: 2,
      type: 'content_upload',
      message: 'New content uploaded by seller',
      time: '5 minutes ago',
      icon: '📄'
    },
    {
      id: 3,
      type: 'order_placed',
      message: 'Order #12345 placed',
      time: '10 minutes ago',
      icon: '🛒'
    },
    {
      id: 4,
      type: 'payment_received',
      message: 'Payment of $99.99 received',
      time: '15 minutes ago',
      icon: '💳'
    }
  ]
};

export const mockUsers = {
  data: [
    {
      id: 1,
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'buyer',
      status: 'active',
      avatar: null,
      createdAt: '2024-01-15T10:30:00Z',
      lastLogin: '2024-01-20T14:22:00Z'
    },
    {
      id: 2,
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'seller',
      status: 'active',
      avatar: null,
      createdAt: '2024-01-10T09:15:00Z',
      lastLogin: '2024-01-20T11:45:00Z'
    },
    {
      id: 3,
      name: 'Mike Johnson',
      email: '<EMAIL>',
      role: 'buyer',
      status: 'inactive',
      avatar: null,
      createdAt: '2024-01-08T16:20:00Z',
      lastLogin: '2024-01-18T08:30:00Z'
    },
    {
      id: 4,
      name: 'Sarah Wilson',
      email: '<EMAIL>',
      role: 'seller',
      status: 'pending',
      avatar: null,
      createdAt: '2024-01-20T12:00:00Z',
      lastLogin: null
    },
    {
      id: 5,
      name: 'Admin User',
      email: '<EMAIL>',
      role: 'admin',
      status: 'active',
      avatar: null,
      createdAt: '2024-01-01T00:00:00Z',
      lastLogin: '2024-01-20T15:00:00Z'
    }
  ],
  totalPages: 5,
  currentPage: 1,
  totalUsers: 25
};

export const mockContent = {
  data: [
    {
      id: 1,
      title: 'Basketball Training Fundamentals',
      description: 'Complete guide to basketball training basics',
      category: 'Basketball',
      price: 29.99,
      status: 'approved',
      seller: {
        id: 2,
        name: 'Jane Smith'
      },
      createdAt: '2024-01-15T10:30:00Z',
      downloads: 45
    },
    {
      id: 2,
      title: 'Football Strategy Playbook',
      description: 'Advanced football strategies and plays',
      category: 'Football',
      price: 49.99,
      status: 'pending',
      seller: {
        id: 4,
        name: 'Sarah Wilson'
      },
      createdAt: '2024-01-18T14:20:00Z',
      downloads: 0
    },
    {
      id: 3,
      title: 'Soccer Coaching Techniques',
      description: 'Professional soccer coaching methods',
      category: 'Soccer',
      price: 39.99,
      status: 'approved',
      seller: {
        id: 2,
        name: 'Jane Smith'
      },
      createdAt: '2024-01-12T09:15:00Z',
      downloads: 78
    }
  ],
  totalPages: 3,
  currentPage: 1,
  totalContent: 15
};

export const mockOrders = {
  data: [
    {
      id: 1,
      orderNumber: 'ORD-2024-001',
      buyer: {
        id: 1,
        name: 'John Doe',
        email: '<EMAIL>'
      },
      content: {
        id: 1,
        title: 'Basketball Training Fundamentals'
      },
      amount: 29.99,
      status: 'completed',
      createdAt: '2024-01-19T10:30:00Z',
      completedAt: '2024-01-19T10:35:00Z'
    },
    {
      id: 2,
      orderNumber: 'ORD-2024-002',
      buyer: {
        id: 3,
        name: 'Mike Johnson',
        email: '<EMAIL>'
      },
      content: {
        id: 3,
        title: 'Soccer Coaching Techniques'
      },
      amount: 39.99,
      status: 'pending',
      createdAt: '2024-01-20T14:20:00Z',
      completedAt: null
    },
    {
      id: 3,
      orderNumber: 'ORD-2024-003',
      buyer: {
        id: 1,
        name: 'John Doe',
        email: '<EMAIL>'
      },
      content: {
        id: 3,
        title: 'Soccer Coaching Techniques'
      },
      amount: 39.99,
      status: 'refunded',
      createdAt: '2024-01-18T16:45:00Z',
      completedAt: '2024-01-18T16:50:00Z'
    }
  ],
  totalPages: 2,
  currentPage: 1,
  totalOrders: 8
};

// Mock API delay function
export const mockApiDelay = (ms = 1000) => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

// Mock API response wrapper
export const mockApiResponse = (data, success = true) => {
  return {
    data,
    success,
    message: success ? 'Success' : 'Error occurred'
  };
};

// Mock error simulation (2% chance of error - reduced for better UX)
export const mockApiError = () => {
  const shouldError = Math.random() < 0.02; // 2% chance (reduced from 10%)
  if (shouldError) {
    const errors = [
      'Network connection failed',
      'Server temporarily unavailable',
      'Database connection timeout',
      'Permission denied',
      'Invalid request parameters'
    ];
    const randomError = errors[Math.floor(Math.random() * errors.length)];
    const error = new Error(randomError);
    error.response = {
      data: {
        message: randomError
      }
    };
    throw error;
  }
};
