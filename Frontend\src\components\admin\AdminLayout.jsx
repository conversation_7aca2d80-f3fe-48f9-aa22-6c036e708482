import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Outlet, useNavigate } from 'react-router-dom';
import AdminSidebar from './AdminSidebar';
import AdminHeader from './AdminHeader';
import '../../styles/AdminLayout.css';

const AdminLayout = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { user, isAuthenticated } = useSelector((state) => state.auth);

  // Temporarily disabled authentication check for development
  // useEffect(() => {
  //   if (!isAuthenticated || user?.role !== 'admin') {
  //     navigate('/auth');
  //     return;
  //   }
  // }, [isAuthenticated, user, navigate]);

  // Handle responsive design
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth <= 768;
      setIsMobile(mobile);

      if (mobile) {
        setSidebarCollapsed(false);
        setSidebarOpen(false);
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const toggleSidebar = () => {
    if (isMobile) {
      setSidebarOpen(!sidebarOpen);
    } else {
      setSidebarCollapsed(!sidebarCollapsed);
    }
  };

  const closeSidebar = () => {
    if (isMobile) {
      setSidebarOpen(false);
    }
  };

  // Temporarily disabled authentication check for development
  // if (!isAuthenticated || user?.role !== 'admin') {
  //   return null;
  // }

  return (
    <div className="admin-layout">
      {/* Sidebar */}
      <AdminSidebar
        collapsed={sidebarCollapsed}
        isOpen={sidebarOpen}
        isMobile={isMobile}
        onClose={closeSidebar}
      />

      {/* Mobile Overlay */}
      {isMobile && sidebarOpen && (
        <div
          className="admin-layout__overlay"
          onClick={closeSidebar}
        />
      )}

      {/* Main Content */}
      <div className={`admin-layout__main ${sidebarCollapsed ? 'admin-layout__main--collapsed' : ''}`}>
        {/* Header */}
        <AdminHeader
          onToggleSidebar={toggleSidebar}
          sidebarCollapsed={sidebarCollapsed}
          isMobile={isMobile}
        />

        {/* Content */}
        <main className="admin-layout__content">
          <div className="admin-layout__content-wrapper">
            <Outlet />
          </div>
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;
